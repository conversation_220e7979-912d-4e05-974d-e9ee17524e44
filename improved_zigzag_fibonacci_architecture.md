# 改进的ZigZag Fibonacci 策略架构

## 1. 整体架构

```mermaid
graph TD
    A[股票数据] --> B[ImprovedZigZagFibonacciSignalGenerator]
    B --> C{信号生成}
    C --> D[ZigZag计算]
    D --> E[Fibonacci回撤]
    E --> F{信号过滤}
    F --> G[趋势强度判断]
    F --> H[波动性过滤]
    F --> I[时间间隔过滤]
    F --> J[成交量确认]
    F --> K[技术指标过滤]
    F --> L[信号质量评估]
    L --> M[交易信号]
    M --> N{风险管理}
    N --> O[动态止损止盈]
    N --> P[仓位管理]
    N --> Q[回撤控制]
    Q --> R[执行交易]
    
    subgraph 核心策略模块
        B
        C
        D
        E
        F
        G
        H
        I
        J
        K
        L
        M
    end
    
    subgraph 风险管理模块
        N
        O
        P
        Q
        R
    end
    
    S[参数优化器] --> B
    T[回测引擎] --> B
    U[可视化模块] --> B
    
    subgraph 辅助模块
        S
        T
        U
    end
```

## 2. 核心组件

### 2.1 ImprovedZigZagFibonacciSignalGenerator

这是改进策略的核心组件，继承自原有的ZigZagFibonacciSignalGenerator，并增加了以下功能：

#### 2.1.1 动态参数调整
- `_calculate_atr()`: 计算平均真实波幅
- `_calculate_zigzag_dynamic()`: 使用动态阈值计算ZigZag点

#### 2.1.2 信号过滤机制
- `_check_trend_strength()`: 检查趋势强度
- `_check_volatility()`: 检查价格波动性
- `_check_volume_confirmation()`: 检查成交量确认
- `_check_rsi_filter()`: RSI过滤
- `_check_macd_filter()`: MACD过滤
- `_check_bollinger_bandwidth()`: 检查布林带宽度
- `_is_signal_interval_valid()`: 检查信号间隔

#### 2.1.3 技术指标计算
- `_calculate_rsi()`: 计算RSI指标
- `_calculate_atr()`: 计算ATR指标

### 2.2 回测引擎

改进的回测引擎支持动态参数调整和增强的风险管理功能：

#### 2.2.1 动态止损止盈
- 根据ATR动态调整止损止盈幅度

#### 2.2.2 仓位管理
- 根据信号强度调整仓位

#### 2.2.3 最大回撤控制
- 当策略达到最大回撤限制时暂停交易

### 2.3 可视化模块

可视化模块支持展示改进策略的所有特性：

#### 2.3.1 ZigZag点标注
- 高点和低点用不同颜色和形状标注

#### 2.3.2 交易信号标记
- 买入和卖出信号用不同符号标记

#### 2.3.3 技术指标显示
- 显示RSI、MACD、布林带等技术指标

## 3. 数据流

### 3.1 信号生成流程

```mermaid
graph LR
    A[股票数据] --> B[ZigZag计算]
    B --> C[Fibonacci回撤]
    C --> D{信号过滤}
    D --> E[趋势强度判断]
    D --> F[波动性过滤]
    D --> G[时间间隔过滤]
    D --> H[成交量确认]
    D --> I[技术指标过滤]
    D --> J[信号质量评估]
    J --> K[交易信号]
```

### 3.2 风险管理流程

```mermaid
graph LR
    A[交易信号] --> B{风险管理}
    B --> C[动态止损止盈]
    B --> D[仓位管理]
    B --> E[回撤控制]
    E --> F[执行交易]
```

## 4. 接口设计

### 4.1 ImprovedZigZagFibonacciSignalGenerator接口

```python
class ImprovedZigZagFibonacciSignalGenerator(ZigZagFibonacciSignalGenerator):
    def __init__(self, zigzag_threshold=0.05, fib_levels=None,
                 dynamic_threshold=False, volume_confirmation=True,
                 rsi_filter=True, macd_filter=True,
                 min_bandwidth=0.02, min_signal_interval=5,
                 atr_period=14, volume_multiplier=1.5,
                 rsi_overbought=70, rsi_oversold=30):
        pass
    
    def generate_signals(self, stock_data: List[StockData]) -> List[TradingSignal]:
        pass
        
    def _calculate_atr(self, stock_data: List[StockData], period: int) -> List[Decimal]:
        pass
        
    def _calculate_zigzag_dynamic(self, stock_data: List[StockData], atr_values: List[Decimal]) -> List[Tuple[int, Decimal, str]]:
        pass
        
    def _check_trend_strength(self, zigzag_points: List[Tuple[int, Decimal, str]]) -> bool:
        pass
        
    def _check_volatility(self, atr_values: List[Decimal], index: int) -> bool:
        pass
        
    def _check_volume_confirmation(self, stock_data: List[StockData], index: int) -> bool:
        pass
        
    def _check_rsi_filter(self, stock_data: List[StockData], index: int) -> bool:
        pass
        
    def _check_macd_filter(self, stock_data: List[StockData], index: int) -> bool:
        pass
        
    def _check_bollinger_bandwidth(self, stock_data: List[StockData], index: int) -> bool:
        pass
        
    def _is_signal_interval_valid(self, current_date) -> bool:
        pass
```

### 4.2 回测引擎接口

```python
class BacktestEngine:
    def __init__(self, initial_capital=100000, commission_rate=0.001,
                 signal_generator=None,
                 stop_loss_pct=0.05, take_profit_pct=0.15):
        pass
    
    def run_backtest(self, stock_data_dict: Dict[str, List[StockData]]) -> Dict[str, Any]:
        pass
        
    def _execute_backtest(self, data: List[StockData], signals: List[TradingSignal]) -> Dict[str, Any]:
        pass
```

## 5. 配置参数

### 5.1 策略参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| zigzag_threshold | 0.05 | ZigZag阈值 |
| fib_levels | [0.236, 0.382, 0.5, 0.618, 0.786] | Fibonacci回撤水平 |
| dynamic_threshold | False | 是否使用动态阈值 |
| volume_confirmation | True | 是否需要成交量确认 |
| rsi_filter | True | 是否使用RSI过滤 |
| macd_filter | True | 是否使用MACD过滤 |
| min_bandwidth | 0.02 | 最小布林带宽度 |
| min_signal_interval | 5 | 最小信号间隔(天) |
| atr_period | 14 | ATR计算周期 |
| volume_multiplier | 1.5 | 成交量倍数阈值 |
| rsi_overbought | 70 | RSI超买阈值 |
| rsi_oversold | 30 | RSI超卖阈值 |

### 5.2 回测参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| initial_capital | 100000 | 初始资金 |
| commission_rate | 0.001 | 手续费率 |
| stop_loss_pct | 0.05 | 止损百分比 |
| take_profit_pct | 0.15 | 止盈百分比 |

## 6. 性能优化

### 6.1 计算优化
- 使用高效的ATR计算算法
- 缓存技术指标计算结果
- 避免重复计算

### 6.2 内存优化
- 及时释放不需要的数据
- 使用生成器减少内存占用
- 优化数据结构

## 7. 扩展性设计

### 7.1 插件化架构
- 支持自定义信号过滤器
- 支持自定义风险管理模块
- 支持自定义参数优化器

### 7.2 配置驱动
- 通过配置文件管理参数
- 支持运行时参数调整
- 支持多策略配置

## 8. 总结

改进的ZigZag Fibonacci策略采用了模块化、可扩展的架构设计，通过多重信号过滤机制和动态参数调整，显著提高了策略的信号质量和风险控制能力。该架构为未来的功能扩展和性能优化奠定了坚实基础。