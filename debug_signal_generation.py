#!/usr/bin/env python3
"""
调试信号生成问题
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator


def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个有明显趋势和回调的价格序列
    prices = [
        # 上涨趋势
        100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
        # 回调
        128, 125, 122, 120, 118,
        # 再次上涨
        120, 123, 126, 130, 134, 138, 142, 145, 148, 150,
        # 回调
        148, 145, 142, 140, 138,
        # 下跌趋势
        138, 135, 132, 128, 125, 122, 118, 115, 112, 110,
        # 反弹
        112, 115, 118, 122, 125, 128, 130
    ]
    
    volumes = [
        # 成交量随着价格波动变化
        1000000, 1100000, 1200000, 1300000, 1400000, 1350000, 1300000, 1400000, 1500000, 1600000, 1700000,
        1500000, 1400000, 1300000, 1200000, 1100000,
        1150000, 1200000, 1250000, 1300000, 1350000, 1400000, 1450000, 1500000, 1550000, 1600000,
        1400000, 1300000, 1200000, 1100000, 1000000,
        1050000, 1100000, 1150000, 1200000, 1250000, 1300000, 1350000, 1400000, 1450000, 1500000,
        1300000, 1200000, 1100000, 1000000, 900000,
        950000, 1000000, 1050000, 1100000, 1150000, 1200000, 1250000
    ]
    
    stock_data = []
    for i, (close_price, volume) in enumerate(zip(prices, volumes)):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
        high_price = max(open_price, close_price) + 0.5
        low_price = min(open_price, close_price) - 0.5
        
        stock_data.append(StockData(
            symbol="SAMPLE",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=volume
        ))
    
    return stock_data


class DebugImprovedZigZagFibonacciSignalGenerator(ImprovedZigZagFibonacciSignalGenerator):
    """调试版本的改进信号生成器"""
    
    def generate_signals_debug(self, stock_data):
        """生成信号并输出调试信息"""
        if len(stock_data) < 20:
            print("数据点不足，需要至少20个数据点")
            return []

        # 计算ATR用于动态阈值和过滤
        atr_values = self._calculate_atr(stock_data, self.atr_period)
        print(f"ATR计算完成，有效值数量: {len([v for v in atr_values if v > 0])}")

        # 计算ZigZag点
        if self.dynamic_threshold and atr_values:
            zigzag_points = self._calculate_zigzag_dynamic(stock_data, atr_values)
        else:
            zigzag_points = self._calculate_zigzag(stock_data)

        print(f"ZigZag点数量: {len(zigzag_points)}")
        if len(zigzag_points) < 3:
            print("ZigZag点不足，需要至少3个点")
            return []

        signals = []
        filter_stats = {
            'total_checked': 0,
            'signal_interval_failed': 0,
            'trend_strength_failed': 0,
            'volatility_failed': 0,
            'bollinger_failed': 0,
            'volume_failed': 0,
            'rsi_failed': 0,
            'macd_failed': 0,
            'fibonacci_failed': 0,
            'signals_generated': 0
        }

        # 遍历股票数据，寻找Fibonacci回撤信号
        for i in range(len(stock_data)):
            current = stock_data[i]
            filter_stats['total_checked'] += 1

            # 检查信号间隔
            if not self._is_signal_interval_valid(current.date):
                filter_stats['signal_interval_failed'] += 1
                continue

            # 获取当前点之前的最近两个ZigZag点
            recent_zigzag = self._get_recent_zigzag_points(zigzag_points, i, 2)

            if len(recent_zigzag) >= 2:
                # 检查趋势强度
                if not self._check_trend_strength(recent_zigzag):
                    filter_stats['trend_strength_failed'] += 1
                    continue

                # 检查波动性
                if not self._check_volatility(atr_values, i):
                    filter_stats['volatility_failed'] += 1
                    continue

                # 检查布林带宽度
                if not self._check_bollinger_bandwidth(stock_data, i):
                    filter_stats['bollinger_failed'] += 1
                    continue

                # 检查成交量确认
                if self.volume_confirmation and not self._check_volume_confirmation(stock_data, i):
                    filter_stats['volume_failed'] += 1
                    continue

                # 检查RSI过滤
                if self.rsi_filter and not self._check_rsi_filter(stock_data, i):
                    filter_stats['rsi_failed'] += 1
                    continue

                # 检查MACD过滤
                if self.macd_filter and not self._check_macd_filter(stock_data, i):
                    filter_stats['macd_failed'] += 1
                    continue

                # 生成Fibonacci信号
                signal = self._check_fibonacci_signal(current, recent_zigzag, i)
                if signal:
                    filter_stats['signals_generated'] += 1
                    self.last_signal_date = current.date
                    signals.append(signal)
                else:
                    filter_stats['fibonacci_failed'] += 1

        # 输出过滤统计
        print("\n=== 过滤条件统计 ===")
        print(f"总检查次数: {filter_stats['total_checked']}")
        print(f"信号间隔过滤: {filter_stats['signal_interval_failed']}")
        print(f"趋势强度过滤: {filter_stats['trend_strength_failed']}")
        print(f"波动性过滤: {filter_stats['volatility_failed']}")
        print(f"布林带宽度过滤: {filter_stats['bollinger_failed']}")
        print(f"成交量过滤: {filter_stats['volume_failed']}")
        print(f"RSI过滤: {filter_stats['rsi_failed']}")
        print(f"MACD过滤: {filter_stats['macd_failed']}")
        print(f"Fibonacci条件不满足: {filter_stats['fibonacci_failed']}")
        print(f"最终生成信号: {filter_stats['signals_generated']}")

        return signals


def main():
    """主函数"""
    print("=== 调试信号生成问题 ===\n")
    
    # 创建示例数据
    stock_data = create_sample_data()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 创建调试版本的策略
    debug_strategy = DebugImprovedZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
        dynamic_threshold=True,  # 使用动态阈值
        volume_confirmation=True,  # 成交量确认
        rsi_filter=True,  # RSI过滤
        macd_filter=False,  # MACD过滤
        min_bandwidth=0.02,  # 最小布林带宽度2%
        min_signal_interval=3,  # 最小信号间隔3天
        atr_period=14,  # ATR周期14天
        volume_multiplier=1.5,  # 成交量倍数1.5
        rsi_overbought=70,  # RSI超买阈值70
        rsi_oversold=30  # RSI超卖阈值30
    )
    
    # 生成信号并调试
    signals = debug_strategy.generate_signals_debug(stock_data)
    
    print(f"\n最终生成 {len(signals)} 个信号")
    
    # 现在尝试放宽条件
    print("\n=== 尝试放宽过滤条件 ===")
    
    relaxed_strategy = DebugImprovedZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
        dynamic_threshold=False,  # 关闭动态阈值
        volume_confirmation=False,  # 关闭成交量确认
        rsi_filter=False,  # 关闭RSI过滤
        macd_filter=False,  # 关闭MACD过滤
        min_bandwidth=0.01,  # 降低布林带宽度要求
        min_signal_interval=1,  # 降低信号间隔要求
        atr_period=14,
        volume_multiplier=1.2,  # 降低成交量倍数要求
        rsi_overbought=80,  # 放宽RSI阈值
        rsi_oversold=20
    )
    
    relaxed_signals = relaxed_strategy.generate_signals_debug(stock_data)
    print(f"\n放宽条件后生成 {len(relaxed_signals)} 个信号")


if __name__ == "__main__":
    main()
