#!/usr/bin/env python3
"""
快速参数优化演示 - 展示如何自动寻找最佳参数
"""

import sys
import os
import random
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from improved_backtest_engine import ImprovedBacktestEngine


def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个更复杂的价格序列，包含多种市场情况
    prices = [
        # 初始上涨
        100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
        # 回调
        128, 125, 122, 120, 118, 115, 112, 110, 108,
        # 强势反弹
        112, 118, 125, 132, 140, 148, 155, 162, 170, 175,
        # 震荡整理
        172, 168, 174, 171, 176, 173, 169, 172, 175, 178,
        # 下跌趋势
        175, 170, 165, 158, 152, 145, 138, 132, 125, 120,
        # 底部反弹
        122, 128, 135, 142, 148, 155, 162, 168, 175, 180,
        # 最终回调
        178, 175, 172, 168, 165, 162, 158, 155, 152, 150
    ]
    
    volumes = [1000000 + random.randint(-200000, 300000) for _ in prices]
    
    stock_data = []
    for i, (close_price, volume) in enumerate(zip(prices, volumes)):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        open_price = close_price + random.uniform(-1, 1)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)
        
        stock_data.append(StockData(
            symbol="SAMPLE",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(close_price)),
            volume=volume
        ))
    
    return stock_data


def test_parameter_combination(params, stock_data):
    """测试单个参数组合"""
    try:
        # 创建策略
        strategy = ImprovedZigZagFibonacciSignalGenerator(
            zigzag_threshold=params['zigzag_threshold'],
            fib_levels=params['fib_levels'],
            dynamic_threshold=params['dynamic_threshold'],
            volume_confirmation=params['volume_confirmation'],
            rsi_filter=params['rsi_filter'],
            macd_filter=False,
            min_bandwidth=params['min_bandwidth'],
            min_signal_interval=params['min_signal_interval'],
            volume_multiplier=params['volume_multiplier'],
            rsi_overbought=params['rsi_overbought'],
            rsi_oversold=params['rsi_oversold']
        )
        
        # 创建回测引擎
        engine = ImprovedBacktestEngine(
            initial_capital=100000,
            commission_rate=params['commission_rate'],
            signal_generator=strategy,
            stop_loss_pct=params['stop_loss_pct'],
            take_profit_pct=params['take_profit_pct']
        )
        
        # 运行回测
        results = engine.run_backtest({'SAMPLE': stock_data})
        performance = results['SAMPLE']['performance']
        
        # 计算评估指标
        total_return = float(performance['total_return'])
        trades_count = len(performance['trades'])
        signal_stats = performance['signal_stats']
        execution_rate = signal_stats['executed_signals'] / max(1, signal_stats['total_signals'])
        
        # 计算买入持有收益率
        buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
        excess_return = total_return - buy_hold_return
        
        return {
            'params': params,
            'total_return': total_return,
            'excess_return': excess_return,
            'trades_count': trades_count,
            'execution_rate': execution_rate,
            'final_value': float(performance['final_value']),
            'signals_total': signal_stats['total_signals'],
            'signals_executed': signal_stats['executed_signals']
        }
        
    except Exception as e:
        return None


def quick_optimization_demo():
    """快速参数优化演示"""
    print("🚀 快速参数优化演示")
    print("=" * 50)
    
    # 创建示例数据
    stock_data = create_sample_data()
    print(f"📊 创建了 {len(stock_data)} 个数据点")
    print(f"   价格范围: ¥{float(stock_data[0].close):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 定义参数搜索空间 (简化版本)
    param_space = {
        'zigzag_threshold': [0.03, 0.05, 0.08, 0.10],
        'fib_levels': [
            [0.236, 0.382, 0.618],
            [0.236, 0.382, 0.5, 0.618],
            [0.236, 0.382, 0.5, 0.618, 0.786]
        ],
        'dynamic_threshold': [True, False],
        'volume_confirmation': [True, False],
        'rsi_filter': [True, False],
        'min_bandwidth': [0.005, 0.01, 0.02],
        'min_signal_interval': [1, 2, 3],
        'volume_multiplier': [1.2, 1.5, 2.0],
        'rsi_overbought': [70, 75, 80],
        'rsi_oversold': [20, 25, 30],
        'stop_loss_pct': [0.05, 0.08, 0.10],
        'take_profit_pct': [0.15, 0.20, 0.25],
        'commission_rate': [0.001, 0.002]
    }
    
    print(f"\n🔍 开始参数优化...")
    print(f"   参数空间大小: {len(param_space)} 个参数")
    
    # 随机搜索优化
    n_trials = 100
    print(f"   随机搜索试验次数: {n_trials}")
    
    results = []
    for i in range(n_trials):
        # 随机选择参数组合
        params = {}
        for param_name, param_values in param_space.items():
            params[param_name] = random.choice(param_values)
        
        # 测试参数组合
        result = test_parameter_combination(params, stock_data)
        if result:
            results.append(result)
        
        if (i + 1) % 20 == 0:
            print(f"   已完成: {i + 1}/{n_trials}")
    
    print(f"✅ 优化完成，获得 {len(results)} 个有效结果")
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    # 显示最佳结果
    print(f"\n🏆 最佳 5 个参数组合:")
    print("=" * 80)
    
    for i, result in enumerate(results[:5]):
        params = result['params']
        print(f"\n第 {i+1} 名:")
        print(f"  💰 总收益率: {result['total_return']:.2%}")
        print(f"  📈 超额收益: {result['excess_return']:.2%}")
        print(f"  🔄 交易次数: {result['trades_count']}")
        print(f"  📊 信号执行率: {result['execution_rate']:.1%}")
        print(f"  💵 最终价值: ¥{result['final_value']:,.2f}")
        
        print(f"  ⚙️  关键参数:")
        print(f"     ZigZag阈值: {params['zigzag_threshold']:.1%}")
        print(f"     Fibonacci水平: {params['fib_levels']}")
        print(f"     止损: {params['stop_loss_pct']:.1%}")
        print(f"     止盈: {params['take_profit_pct']:.1%}")
        print(f"     动态阈值: {params['dynamic_threshold']}")
        print(f"     成交量确认: {params['volume_confirmation']}")
        print(f"     RSI过滤: {params['rsi_filter']}")
        print(f"     信号间隔: {params['min_signal_interval']}天")
    
    # 分析参数重要性
    print(f"\n📈 参数重要性分析:")
    print("=" * 50)
    
    # 分析最佳参数的分布
    top_10 = results[:10]
    
    for param_name in ['zigzag_threshold', 'stop_loss_pct', 'take_profit_pct', 'dynamic_threshold']:
        param_values = [r['params'][param_name] for r in top_10]
        if isinstance(param_values[0], bool):
            true_count = sum(param_values)
            print(f"  {param_name}: True={true_count}/10, False={10-true_count}/10")
        else:
            avg_value = sum(param_values) / len(param_values)
            print(f"  {param_name}: 平均值={avg_value:.3f}")
    
    # 给出建议
    print(f"\n💡 优化建议:")
    print("=" * 30)
    
    best_result = results[0]
    best_params = best_result['params']
    
    print(f"1. 推荐使用第1名的参数组合")
    print(f"2. ZigZag阈值建议: {best_params['zigzag_threshold']:.1%}")
    print(f"3. 止损止盈建议: {best_params['stop_loss_pct']:.1%} / {best_params['take_profit_pct']:.1%}")
    print(f"4. 过滤条件建议:")
    print(f"   - 动态阈值: {best_params['dynamic_threshold']}")
    print(f"   - 成交量确认: {best_params['volume_confirmation']}")
    print(f"   - RSI过滤: {best_params['rsi_filter']}")
    
    print(f"\n🔧 如何应用这些参数:")
    print("=" * 30)
    print("在你的策略中使用以下代码:")
    print(f"""
strategy = ImprovedZigZagFibonacciSignalGenerator(
    zigzag_threshold={best_params['zigzag_threshold']},
    fib_levels={best_params['fib_levels']},
    dynamic_threshold={best_params['dynamic_threshold']},
    volume_confirmation={best_params['volume_confirmation']},
    rsi_filter={best_params['rsi_filter']},
    min_bandwidth={best_params['min_bandwidth']},
    min_signal_interval={best_params['min_signal_interval']},
    volume_multiplier={best_params['volume_multiplier']},
    rsi_overbought={best_params['rsi_overbought']},
    rsi_oversold={best_params['rsi_oversold']}
)

engine = ImprovedBacktestEngine(
    initial_capital=100000,
    commission_rate={best_params['commission_rate']},
    signal_generator=strategy,
    stop_loss_pct={best_params['stop_loss_pct']},
    take_profit_pct={best_params['take_profit_pct']}
)
""")


if __name__ == "__main__":
    quick_optimization_demo()
