#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ZigZag算法与真实股票数据
当网络可用时，使用真实股票数据进行测试
"""

from zigzag_echarts_demo import <PERSON>igZagAnalyzer, EChartsVisualizer, create_sample_stock_data
from stock_backtest import get_stock_data, dataframe_to_stock_data


def test_with_real_data():
    """使用真实股票数据测试"""
    print("=== 使用真实股票数据测试ZigZag算法 ===\n")
    
    # 测试多个股票
    test_stocks = [
        {"code": "000001", "name": "平安银行"},
        {"code": "000002", "name": "万科A"},
        {"code": "600000", "name": "浦发银行"},
        {"code": "600036", "name": "招商银行"},
        {"code": "003021", "name": "兆威机电"},
    ]
    
    start_date = "20240101"
    end_date = "20241201"
    
    successful_tests = 0
    
    for stock in test_stocks:
        print(f"\n📈 测试股票: {stock['code']} - {stock['name']}")
        
        try:
            # 获取股票数据
            df = get_stock_data(stock['code'], start_date, end_date)
            stock_data = dataframe_to_stock_data(df, stock['code'])
            
            if not stock_data:
                print(f"   ❌ 无法获取 {stock['code']} 的数据")
                continue
            
            print(f"   ✅ 获取到 {len(stock_data)} 个数据点")
            print(f"   📊 价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
            
            # 使用动态阈值分析
            analyzer = ZigZagAnalyzer(
                base_threshold=0.05,
                dynamic_threshold=True,
                atr_period=14
            )
            
            zigzag_points = analyzer.calculate_zigzag_points(stock_data)
            
            if zigzag_points:
                peaks = [p for p in zigzag_points if p[2] == 'HIGH']
                valleys = [p for p in zigzag_points if p[2] == 'LOW']
                
                print(f"   🔍 发现转折点: {len(zigzag_points)}个")
                print(f"   📈 峰点: {len(peaks)}个")
                print(f"   📉 谷点: {len(valleys)}个")
                
                # 生成可视化
                visualizer = EChartsVisualizer()
                chart_data = visualizer.generate_chart_data(stock_data, zigzag_points)
                
                output_file = f"zigzag_real_{stock['code']}_{stock['name']}.html"
                visualizer.create_html_chart(
                    chart_data, 
                    f"{stock['code']} - {stock['name']}", 
                    output_file
                )
                
                print(f"   📄 可视化文件: {output_file}")
                successful_tests += 1
            else:
                print(f"   ⚠️  未发现转折点")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print(f"\n🎉 测试完成! 成功测试了 {successful_tests} 个股票")
    return successful_tests > 0


def test_with_sample_data():
    """使用示例数据测试"""
    print("=== 使用示例数据测试ZigZag算法 ===\n")
    
    # 创建示例数据
    stock_data = create_sample_stock_data()
    print(f"📊 生成示例数据: {len(stock_data)} 个数据点")
    
    # 测试不同配置
    configs = [
        {"threshold": 0.03, "dynamic": True, "name": "动态阈值(3%基准)"},
        {"threshold": 0.05, "dynamic": True, "name": "动态阈值(5%基准)"},
        {"threshold": 0.08, "dynamic": True, "name": "动态阈值(8%基准)"},
        {"threshold": 0.05, "dynamic": False, "name": "固定阈值(5%)"},
    ]
    
    for config in configs:
        print(f"\n🔧 测试配置: {config['name']}")
        
        analyzer = ZigZagAnalyzer(
            base_threshold=config['threshold'],
            dynamic_threshold=config['dynamic'],
            atr_period=14
        )
        
        zigzag_points = analyzer.calculate_zigzag_points(stock_data)
        
        if zigzag_points:
            peaks = [p for p in zigzag_points if p[2] == 'HIGH']
            valleys = [p for p in zigzag_points if p[2] == 'LOW']
            
            print(f"   🔍 转折点: {len(zigzag_points)}个 (峰:{len(peaks)}, 谷:{len(valleys)})")
            
            # 显示前几个转折点
            print("   📋 前5个转折点:")
            for i, (idx, price, point_type, date) in enumerate(zigzag_points[:5]):
                print(f"      {i+1}. {date} - {point_type}: ¥{float(price):.2f}")
            
            # 计算一些统计信息
            if len(zigzag_points) > 1:
                price_changes = []
                for i in range(1, len(zigzag_points)):
                    prev_price = float(zigzag_points[i-1][1])
                    curr_price = float(zigzag_points[i][1])
                    change = abs(curr_price - prev_price) / prev_price
                    price_changes.append(change)
                
                avg_change = sum(price_changes) / len(price_changes)
                max_change = max(price_changes)
                min_change = min(price_changes)
                
                print(f"   📈 平均波动幅度: {avg_change:.2%}")
                print(f"   📊 最大波动幅度: {max_change:.2%}")
                print(f"   📉 最小波动幅度: {min_change:.2%}")
        else:
            print("   ⚠️  未发现转折点")


def analyze_zigzag_performance():
    """分析ZigZag算法性能"""
    print("=== ZigZag算法性能分析 ===\n")
    
    stock_data = create_sample_stock_data()
    
    # 测试不同阈值的效果
    thresholds = [0.02, 0.03, 0.05, 0.08, 0.10]
    
    print("📊 不同阈值的转折点数量对比:")
    print("阈值\t动态\t固定\t差异")
    print("-" * 30)
    
    for threshold in thresholds:
        # 动态阈值
        analyzer_dynamic = ZigZagAnalyzer(
            base_threshold=threshold,
            dynamic_threshold=True,
            atr_period=14
        )
        zigzag_dynamic = analyzer_dynamic.calculate_zigzag_points(stock_data)
        
        # 固定阈值
        analyzer_fixed = ZigZagAnalyzer(
            base_threshold=threshold,
            dynamic_threshold=False,
            atr_period=14
        )
        zigzag_fixed = analyzer_fixed.calculate_zigzag_points(stock_data)
        
        dynamic_count = len(zigzag_dynamic)
        fixed_count = len(zigzag_fixed)
        diff = dynamic_count - fixed_count
        
        print(f"{threshold:.1%}\t{dynamic_count}\t{fixed_count}\t{diff:+d}")
    
    print("\n💡 分析结论:")
    print("   - 动态阈值通常能发现更多的转折点")
    print("   - 较小的阈值会产生更多的转折点（更敏感）")
    print("   - 较大的阈值会过滤掉小的波动（更稳定）")


def main():
    """主函数"""
    print("🚀 ZigZag算法综合测试\n")
    
    # 首先尝试真实数据
    real_data_success = False
    try:
        real_data_success = test_with_real_data()
    except Exception as e:
        print(f"真实数据测试失败: {e}")
    
    # 如果真实数据失败，使用示例数据
    if not real_data_success:
        print("\n" + "="*50)
        test_with_sample_data()
    
    print("\n" + "="*50)
    analyze_zigzag_performance()
    
    print("\n🎯 测试总结:")
    print("✅ ZigZag算法实现完成")
    print("✅ 支持动态阈值计算")
    print("✅ 支持ECharts可视化")
    print("✅ 支持峰谷点标注")
    print("✅ 支持趋势线分析")
    print("✅ 支持交互式图表")
    
    print("\n📁 生成的文件:")
    import os
    html_files = [f for f in os.listdir('.') if f.startswith('zigzag_') and f.endswith('.html')]
    for file in html_files:
        print(f"   📄 {file}")


if __name__ == "__main__":
    main()
