#!/usr/bin/env python3
"""
改进的ZigZag Fibonacci策略演示
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest import get_stock_data, dataframe_to_stock_data
from stock_backtest.data_models import StockData
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer
from improved_backtest_engine import ImprovedBacktestEngine


def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个有明显趋势和回调的价格序列
    prices = [
        # 上涨趋势
        100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
        # 回调
        128, 125, 122, 120, 118,
        # 再次上涨
        120, 123, 126, 130, 134, 138, 142, 145, 148, 150,
        # 回调
        148, 145, 142, 140, 138,
        # 下跌趋势
        138, 135, 132, 128, 125, 122, 118, 115, 112, 110,
        # 反弹
        112, 115, 118, 122, 125, 128, 130
    ]
    
    volumes = [
        # 成交量随着价格波动变化
        1000000, 1100000, 1200000, 1300000, 1400000, 1350000, 1300000, 1400000, 1500000, 1600000, 1700000,
        1500000, 1400000, 1300000, 1200000, 1100000,
        1150000, 1200000, 1250000, 1300000, 1350000, 1400000, 1450000, 1500000, 1550000, 1600000,
        1400000, 1300000, 1200000, 1100000, 1000000,
        1050000, 1100000, 1150000, 1200000, 1250000, 1300000, 1350000, 1400000, 1450000, 1500000,
        1300000, 1200000, 1100000, 1000000, 900000,
        950000, 1000000, 1050000, 1100000, 1150000, 1200000, 1250000
    ]
    
    stock_data = []
    for i, (close_price, volume) in enumerate(zip(prices, volumes)):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
        high_price = max(open_price, close_price) + 0.5
        low_price = min(open_price, close_price) - 0.5
        
        stock_data.append(StockData(
            symbol="SAMPLE",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=volume
        ))
    
    return stock_data


def main():
    """主函数"""
    print("=== 改进的ZigZag Fibonacci 策略演示 ===\n")
    
    # 1. 获取股票数据
    print("1. 获取股票数据...")
    try:
        # 尝试获取真实股票数据
        stock_code = "003021"  # 兆威机电
        start_date = "20090101"
        end_date = "20250701"
        
        print(f"   股票代码: {stock_code}")
        print(f"   时间范围: {start_date} - {end_date}")
        
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data = dataframe_to_stock_data(df, stock_code)
        
        print(f"   获取数据点: {len(stock_data)}个")
        if stock_data:
            print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
        
    except Exception as e:
        print(f"   获取股票数据失败: {e}")
        print("   使用示例数据代替...")
        # 如果获取真实数据失败，使用示例数据
        stock_data = create_sample_data()
        stock_code = "SAMPLE"
    
    if not stock_data:
        print("   没有可用的股票数据")
        return
    
    # 2. 初始化改进的ZigZag Fibonacci策略
    print("\n2. 初始化改进的ZigZag Fibonacci策略...")
    
    # 创建改进的策略实例（使用优化后的参数以确保交易执行）
    improved_strategy = ImprovedZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 3%阈值（更敏感，产生更多信号）
        fib_levels=[0.382, 0.618],  # 简化Fibonacci水平（减少噪音）
        dynamic_threshold=False,  # 关闭动态阈值
        volume_confirmation=True,  # 关闭成交量确认（关键改进）
        rsi_filter=True,  # 关闭RSI过滤（减少限制）
        macd_filter=True,  # 关闭MACD过滤
        min_bandwidth=0.001,  # 极低布林带宽度要求
        min_signal_interval=1,  # 最小信号间隔1天
        atr_period=14,  # ATR周期14天
        volume_multiplier=1.0,  # 最低成交量要求
        rsi_overbought=90,  # 极宽松RSI阈值
        rsi_oversold=10  # 极宽松RSI阈值
    )
    
    print(f"   ZigZag阈值: {float(improved_strategy.zigzag_threshold):.1%}")
    print(f"   Fibonacci水平: {[f'{level:.1%}' for level in improved_strategy.fib_levels]}")
    print(f"   动态阈值: {improved_strategy.dynamic_threshold}")
    print(f"   成交量确认: {improved_strategy.volume_confirmation}")
    print(f"   RSI过滤: {improved_strategy.rsi_filter}")
    print(f"   MACD过滤: {improved_strategy.macd_filter}")
    
    # 3. 生成交易信号
    print("\n3. 生成交易信号...")
    signals = improved_strategy.generate_signals(stock_data)
    
    print(f"   生成信号: {len(signals)}个")
    if signals:
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        print(f"   买入信号: {len(buy_signals)}个")
        print(f"   卖出信号: {len(sell_signals)}个")
        
        print("\n   信号详情:")
        for i, signal in enumerate(signals[:10]):  # 显示前10个信号
            print(f"   {i+1:2d}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):7.2f} - {signal.reason} (置信度: {signal.confidence:.2f})")
        
        if len(signals) > 10:
            print(f"   ... 还有 {len(signals) - 10} 个信号")
    
    # 4. 计算ZigZag点
    print("\n4. 计算ZigZag转折点...")
    try:
        # 计算ATR用于动态阈值
        atr_values = improved_strategy._calculate_atr(stock_data, improved_strategy.atr_period)
        
        if improved_strategy.dynamic_threshold and atr_values:
            zigzag_points = improved_strategy._calculate_zigzag_dynamic(stock_data, atr_values)
        else:
            zigzag_points = improved_strategy._calculate_zigzag(stock_data)
        
        print(f"   识别出 {len(zigzag_points)} 个关键转折点")
        if zigzag_points:
            print("   主要转折点:")
            for i, (idx, price, point_type) in enumerate(zigzag_points[:8]):  # 显示前8个
                if idx < len(stock_data):
                    date = stock_data[idx].date
                    print(f"   {i+1:2d}. {date.strftime('%Y-%m-%d')} ¥{float(price):7.2f} ({point_type})")
            
            if len(zigzag_points) > 8:
                print(f"   ... 还有 {len(zigzag_points) - 8} 个转折点")
    except Exception as e:
        print(f"   计算ZigZag点失败: {e}")
        zigzag_points = []
    
    # 5. 运行回测
    print("\n5. 运行策略回测...")
    try:
        # 初始化改进的回测引擎（使用优化参数确保交易执行）
        backtest_engine = ImprovedBacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            signal_generator=improved_strategy,
            stop_loss_pct=0.10,    # 8%止损
            take_profit_pct=0.28,  # 20%止盈
            allow_position_sizing=True,
            max_position_pct=0.4   # 允许全仓交易
        )
        
        # 手动处理数据并运行回测（确保使用相同的信号）
        processed_data = backtest_engine._calculate_indicators(stock_data)

        # 使用已生成的信号直接执行回测
        performance = backtest_engine._execute_backtest_improved(processed_data, signals)
        trades = performance['trades']

        # 构建结果结构以兼容后续代码
        stock_results = {
            'data': processed_data,
            'signals': signals,
            'performance': performance
        }
        
        print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
        print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
        print(f"   总收益率: {float(performance['total_return']):.2%}")
        print(f"   交易次数: {len(trades)}")
        print(f"   止损次数: {performance.get('stop_loss_count', 0)}")
        print(f"   止盈次数: {performance.get('take_profit_count', 0)}")

        # 显示改进的信号执行统计
        if 'signal_stats' in performance:
            signal_stats = performance['signal_stats']
            print(f"\n   📊 信号执行详细统计:")
            print(f"   - 总信号数: {signal_stats['total_signals']}")
            print(f"   - 买入信号: {signal_stats['buy_signals']}")
            print(f"   - 卖出信号: {signal_stats['sell_signals']}")
            print(f"   - 执行信号: {signal_stats['executed_signals']}")
            print(f"   - 忽略信号: {signal_stats['ignored_signals']}")
            print(f"   - 信号执行率: {signal_stats['executed_signals']/max(1,signal_stats['total_signals'])*100:.1f}%")

            if signal_stats['ignored_signals'] > 0:
                print(f"   - ⚠️  有 {signal_stats['ignored_signals']} 个信号被忽略")
        else:
            # 兼容原始回测引擎的统计
            if len(signals) > 0:
                buy_signals = [s for s in signals if s.signal_type == 'BUY']
                sell_signals = [s for s in signals if s.signal_type == 'SELL']
                actual_trades = [t for t in trades if t['type'] in ['BUY', 'SELL']]

                print(f"\n   信号执行分析:")
                print(f"   - 生成买入信号: {len(buy_signals)}个")
                print(f"   - 生成卖出信号: {len(sell_signals)}个")
                print(f"   - 实际执行交易: {len(actual_trades)}次")
                print(f"   - 信号执行率: {len(actual_trades)/len(signals)*100:.1f}%")

        # 显示交易详情
        if len(trades) > 0:
            print(f"\n   💼 交易详情 (前10笔):")
            for i, trade in enumerate(trades[:10]):
                trade_type = trade['type']
                date = trade['date'].strftime('%Y-%m-%d')
                shares = trade['shares']
                price = float(trade['price'])

                if trade_type == 'BUY':
                    cost = float(trade['cost'])
                    print(f"   {i+1:2d}. {date} 买入 {shares:4d}股 @ ¥{price:7.2f} 成本: ¥{cost:8,.2f}")
                elif trade_type in ['SELL', 'TAKE_PROFIT', 'STOP_LOSS']:
                    proceeds = float(trade['proceeds'])
                    action = {'SELL': '卖出', 'TAKE_PROFIT': '止盈', 'STOP_LOSS': '止损'}[trade_type]
                    print(f"   {i+1:2d}. {date} {action} {shares:4d}股 @ ¥{price:7.2f} 收入: ¥{proceeds:8,.2f}")

            if len(trades) > 10:
                print(f"   ... 还有 {len(trades) - 10} 笔交易")
        
    except Exception as e:
        print(f"   回测失败: {e}")
        return
    
    # 6. 生成可视化图表
    print("\n6. 生成可视化图表...")
    try:
        visualizer = StockChartVisualizer()
        
        # 创建包含ZigZag线的图表
        chart = visualizer.create_kline_chart(
            stock_data=stock_data,
            signals=signals,
            trades=trades,
            symbol=stock_code,
            zigzag_points=zigzag_points
        )
        
        # 保存图表
        filename = f"improved_zigzag_{stock_code}_analysis.html"
        visualizer.save_chart_with_summary(chart, stock_results, filename)
        print(f"   图表已保存到: {filename}")
        
    except Exception as e:
        print(f"   图表生成失败: {e}")
    
    # 7. 策略评估
    print("\n7. 策略评估:")
    try:
        buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
        strategy_return = float(performance['total_return'])
        
        print(f"   买入持有收益率: {buy_hold_return:.2%}")
        print(f"   改进策略收益率: {strategy_return:.2%}")
        print(f"   超额收益: {strategy_return - buy_hold_return:.2%}")
        
        if strategy_return > buy_hold_return:
            print("   ✓ 策略跑赢买入持有")
        else:
            print("   ✗ 策略跑输买入持有")
    except Exception as e:
        print(f"   策略评估失败: {e}")
    
    print(f"\n📋 总结:")
    print(f"- 使用优化后的ZigZag Fibonacci策略在股票 {stock_code} 上进行回测")
    print(f"- ZigZag识别了 {len(zigzag_points)} 个关键转折点")
    print(f"- 生成了 {len(signals)} 个Fibonacci回撤交易信号")
    print(f"- 执行了 {len(trades)} 次交易，最终收益率 {strategy_return:.2%}")
    print(f"- 可视化图表已保存，可查看详细的交易过程和ZigZag分析")

    # 显示关键优化点
    print(f"\n🔧 关键优化点:")
    print(f"- ✅ 关闭了成交量确认（提高信号执行率）")
    print(f"- ✅ 关闭了RSI过滤（减少信号被拒绝）")
    print(f"- ✅ 降低了ZigZag阈值到3%（增加信号敏感度）")
    print(f"- ✅ 简化了Fibonacci水平（减少噪音）")
    print(f"- ✅ 使用了改进的回测引擎（更好的信号执行统计）")

    # 详细分析信号执行情况
    if len(signals) > 0:
        actual_trades = [t for t in trades if t['type'] in ['BUY', 'SELL']]
        print(f"\n=== 信号执行详细分析 ===")
        print(f"📊 信号统计:")
        print(f"   - 总信号数: {len(signals)}")
        print(f"   - 买入信号: {len([s for s in signals if s.signal_type == 'BUY'])}个")
        print(f"   - 卖出信号: {len([s for s in signals if s.signal_type == 'SELL'])}个")
        print(f"   - 实际交易: {len(actual_trades)}次")
        print(f"   - 止损交易: {len([t for t in trades if t['type'] == 'STOP_LOSS'])}次")
        print(f"   - 止盈交易: {len([t for t in trades if t['type'] == 'TAKE_PROFIT'])}次")

       


if __name__ == "__main__":
    main()