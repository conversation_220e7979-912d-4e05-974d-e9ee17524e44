# 项目依赖关系

## 当前依赖

```toml
[project]
name = "msnowball"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aio-pika>=9.5.5",
    "akshare>=1.17.26",
    "pandas>=2.3.1",
    "pyecharts>=2.0.8",
]
```

## 改进策略新增依赖

由于改进的策略使用了numpy库进行数值计算，需要添加以下依赖：

```toml
dependencies = [
    "aio-pika>=9.5.5",
    "akshare>=1.17.26",
    "pandas>=2.3.1",
    "pyecharts>=2.0.8",
    "numpy>=1.24.0",  # 新增：用于数值计算
]
```

## 依赖说明

### aio-pika
用于异步RabbitMQ消息队列通信

### akshare
用于获取股票数据

### pandas
用于数据处理和分析

### pyecharts
用于数据可视化

### numpy
用于数值计算（新增）