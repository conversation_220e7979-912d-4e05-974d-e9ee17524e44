#!/usr/bin/env python3
"""
改进的回测引擎 - 支持更灵活的信号执行
"""

from typing import List, Dict, Any, Union
from decimal import Decimal
from datetime import datetime
from stock_backtest.data_models import StockData, TradingSignal
from stock_backtest.indicators import TechnicalIndicators
from stock_backtest.signal_generator import SignalGenerator, ZigZagFibonacciSignalGenerator
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator


class ImprovedBacktestEngine:
    """改进的回测引擎，支持更灵活的信号执行"""

    def __init__(self, initial_capital=100000, commission_rate=0.001,
                 signal_generator: Union[SignalGenerator, ZigZagFibonacciSignalGenerator, ImprovedZigZagFibonacciSignalGenerator] = None,
                 stop_loss_pct=0.05, take_profit_pct=0.15,
                 allow_position_sizing=False,  # 是否允许仓位管理
                 max_position_pct=1.0):       # 最大仓位比例
        self.initial_capital = Decimal(str(initial_capital))
        self.commission_rate = Decimal(str(commission_rate))
        self.signal_generator = signal_generator or SignalGenerator()
        self.stop_loss_pct = Decimal(str(stop_loss_pct))
        self.take_profit_pct = Decimal(str(take_profit_pct))
        self.allow_position_sizing = allow_position_sizing
        self.max_position_pct = Decimal(str(max_position_pct))
        
    def run_backtest(self, stock_data_dict: Dict[str, List[StockData]]) -> Dict[str, Any]:
        """运行回测"""
        results = {}

        for symbol, data in stock_data_dict.items():
            # 计算技术指标
            processed_data = self._calculate_indicators(data)

            # 生成交易信号
            signals = self.signal_generator.generate_signals(processed_data)

            # 执行回测
            backtest_result = self._execute_backtest_improved(processed_data, signals)

            # 构建结果
            result_data = {
                'data': processed_data,
                'signals': signals,
                'performance': backtest_result
            }

            # 如果是ZigZag策略，添加ZigZag点信息
            if hasattr(self.signal_generator, '_calculate_zigzag'):
                zigzag_points = self.signal_generator._calculate_zigzag(processed_data)
                result_data['zigzag_points'] = zigzag_points

            results[symbol] = result_data

        return results
    
    def _calculate_indicators(self, data: List[StockData]) -> List[StockData]:
        """计算技术指标"""
        prices = [item.close for item in data]
        
        # 计算MACD
        macd, signal, histogram = TechnicalIndicators.calculate_macd(prices)
        
        # 计算布林线
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.calculate_bollinger_bands(prices, period=9, std_dev=1)
        
        # 更新数据
        enhanced_data = []
        for i, item in enumerate(data):
            new_item = StockData(
                symbol=item.symbol,
                date=item.date,
                open=item.open,
                high=item.high,
                low=item.low,
                close=item.close,
                volume=item.volume,
                macd=macd[i],
                macd_signal=signal[i],
                macd_histogram=histogram[i],
                bb_upper=bb_upper[i],
                bb_middle=bb_middle[i],
                bb_lower=bb_lower[i]
            )
            enhanced_data.append(new_item)
        
        return enhanced_data
    
    def _execute_backtest_improved(self, data: List[StockData], signals: List[TradingSignal]) -> Dict[str, Any]:
        """改进的回测执行逻辑"""
        capital = self.initial_capital
        position = 0
        trades = []
        buy_price = None
        
        # 统计信息
        signal_stats = {
            'total_signals': len(signals),
            'buy_signals': len([s for s in signals if s.signal_type == 'BUY']),
            'sell_signals': len([s for s in signals if s.signal_type == 'SELL']),
            'executed_signals': 0,
            'ignored_signals': 0,
            'ignored_reasons': []
        }

        signal_dict = {signal.date: signal for signal in signals}

        for i, stock_data in enumerate(data):
            # 检查止损和止盈条件
            if position > 0 and buy_price is not None:
                price_decline = (buy_price - stock_data.low) / buy_price
                price_gain = (stock_data.high - buy_price) / buy_price

                # 检查止盈条件
                if price_gain >= self.take_profit_pct:
                    take_profit_price = stock_data.high
                    proceeds = position * take_profit_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'TAKE_PROFIT',
                        'shares': position,
                        'price': take_profit_price,
                        'proceeds': proceeds,
                        'reason': f'止盈卖出 (涨幅: {price_gain:.2%})'
                    })
                    position = 0
                    buy_price = None
                    continue

                # 检查止损条件
                elif price_decline >= self.stop_loss_pct:
                    stop_loss_price = stock_data.low
                    proceeds = position * stop_loss_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'STOP_LOSS',
                        'shares': position,
                        'price': stop_loss_price,
                        'proceeds': proceeds,
                        'reason': f'止损卖出 (跌幅: {price_decline:.2%})'
                    })
                    position = 0
                    buy_price = None
                    continue

            # 处理交易信号
            if stock_data.date in signal_dict:
                signal = signal_dict[stock_data.date]
                executed = False
                ignore_reason = None

                if signal.signal_type == 'BUY':
                    if position == 0 and capital > 0:
                        # 可以买入
                        max_shares = int(capital * self.max_position_pct / (signal.price * (Decimal(1) + self.commission_rate)))
                        if max_shares > 0:
                            cost = max_shares * signal.price * (Decimal(1) + self.commission_rate)
                            capital -= cost
                            position += max_shares
                            buy_price = signal.price
                            trades.append({
                                'date': signal.date,
                                'type': 'BUY',
                                'shares': max_shares,
                                'price': signal.price,
                                'cost': cost,
                                'reason': signal.reason
                            })
                            executed = True
                        else:
                            ignore_reason = "资金不足"
                    elif position > 0:
                        ignore_reason = "已有持仓"
                    else:
                        ignore_reason = "资金不足"

                elif signal.signal_type == 'SELL':
                    if position > 0:
                        # 可以卖出
                        proceeds = position * signal.price * (Decimal(1) - self.commission_rate)
                        capital += proceeds

                        trades.append({
                            'date': signal.date,
                            'type': 'SELL',
                            'shares': position,
                            'price': signal.price,
                            'proceeds': proceeds,
                            'reason': signal.reason
                        })
                        position = 0
                        buy_price = None
                        executed = True
                    else:
                        ignore_reason = "无持仓"

                # 更新统计
                if executed:
                    signal_stats['executed_signals'] += 1
                else:
                    signal_stats['ignored_signals'] += 1
                    signal_stats['ignored_reasons'].append(f"{signal.date.strftime('%Y-%m-%d')} {signal.signal_type}: {ignore_reason}")

        # 计算最终价值
        final_price = data[-1].close if data else Decimal(0)
        final_value = capital + position * final_price

        # 统计止损和止盈次数
        stop_loss_trades = [t for t in trades if t['type'] == 'STOP_LOSS']
        take_profit_trades = [t for t in trades if t['type'] == 'TAKE_PROFIT']

        return {
            'initial_capital': self.initial_capital,
            'final_value': final_value,
            'total_return': (final_value - self.initial_capital) / self.initial_capital,
            'trades': trades,
            'final_position': position,
            'stop_loss_count': len(stop_loss_trades),
            'stop_loss_pct': float(self.stop_loss_pct),
            'take_profit_count': len(take_profit_trades),
            'take_profit_pct': float(self.take_profit_pct),
            'signal_stats': signal_stats
        }


def demo_improved_backtest():
    """演示改进的回测引擎"""
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from stock_backtest import get_stock_data, dataframe_to_stock_data
    from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
    
    print("=== 改进回测引擎演示 ===\n")
    
    # 获取股票数据
    try:
        stock_code = "003021"
        start_date = "20200101"
        end_date = "20240101"
        
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data = dataframe_to_stock_data(df, stock_code)
        print(f"获取到 {len(stock_data)} 个数据点")
        
    except Exception as e:
        print(f"获取数据失败: {e}")
        return
    
    # 创建策略
    strategy = ImprovedZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
        dynamic_threshold=False,
        volume_confirmation=False,
        rsi_filter=False,
        macd_filter=False,
        min_bandwidth=0.005,
        min_signal_interval=1
    )
    
    # 创建改进的回测引擎
    improved_engine = ImprovedBacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=strategy,
        stop_loss_pct=0.08,
        take_profit_pct=0.20,
        allow_position_sizing=True,
        max_position_pct=1.0
    )
    
    # 运行回测
    results = improved_engine.run_backtest({stock_code: stock_data})
    performance = results[stock_code]['performance']
    signal_stats = performance['signal_stats']
    
    print(f"\n=== 回测结果 ===")
    print(f"初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"总收益率: {float(performance['total_return']):.2%}")
    print(f"交易次数: {len(performance['trades'])}")
    
    print(f"\n=== 信号执行统计 ===")
    print(f"总信号数: {signal_stats['total_signals']}")
    print(f"买入信号: {signal_stats['buy_signals']}")
    print(f"卖出信号: {signal_stats['sell_signals']}")
    print(f"执行信号: {signal_stats['executed_signals']}")
    print(f"忽略信号: {signal_stats['ignored_signals']}")
    print(f"执行率: {signal_stats['executed_signals']/signal_stats['total_signals']*100:.1f}%")
    
    if signal_stats['ignored_reasons']:
        print(f"\n前10个被忽略的信号:")
        for reason in signal_stats['ignored_reasons'][:10]:
            print(f"  - {reason}")


if __name__ == "__main__":
    demo_improved_backtest()
