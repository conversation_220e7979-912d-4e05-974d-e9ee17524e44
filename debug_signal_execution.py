#!/usr/bin/env python3
"""
调试信号执行问题 - 详细分析为什么有信号但没有交易
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine


def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个有明显趋势和回调的价格序列
    prices = [
        # 上涨趋势
        100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
        # 回调
        128, 125, 122, 120, 118,
        # 再次上涨
        120, 123, 126, 130, 134, 138, 142, 145, 148, 150,
        # 回调
        148, 145, 142, 140, 138,
        # 下跌趋势
        138, 135, 132, 128, 125, 122, 118, 115, 112, 110,
        # 反弹
        112, 115, 118, 122, 125, 128, 130
    ]
    
    volumes = [1000000] * len(prices)  # 简化成交量
    
    stock_data = []
    for i, (close_price, volume) in enumerate(zip(prices, volumes)):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
        high_price = max(open_price, close_price) + 0.5
        low_price = min(open_price, close_price) - 0.5
        
        stock_data.append(StockData(
            symbol="SAMPLE",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=volume
        ))
    
    return stock_data


def analyze_signal_execution():
    """分析信号执行情况"""
    print("=== 信号执行分析 ===\n")
    
    # 创建示例数据
    stock_data = create_sample_data()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 创建策略（使用当前demo的参数）
    strategy = ImprovedZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
        dynamic_threshold=False,  # 关闭动态阈值
        volume_confirmation=False,  # 关闭成交量确认
        rsi_filter=False,  # 关闭RSI过滤
        macd_filter=False,  # 关闭MACD过滤
        min_bandwidth=0.005,  # 最小布林带宽度0.5%
        min_signal_interval=1,  # 最小信号间隔1天
        atr_period=14,  # ATR周期14天
        volume_multiplier=1.1,  # 成交量倍数1.1
        rsi_overbought=80,  # RSI超买阈值80
        rsi_oversold=20  # RSI超卖阈值20
    )
    
    # 创建回测引擎
    engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=strategy,
        stop_loss_pct=0.08,    # 8%止损
        take_profit_pct=0.20   # 20%止盈
    )
    
    # 处理数据（添加技术指标）
    processed_data = engine._calculate_indicators(stock_data)
    
    # 生成信号
    signals = strategy.generate_signals(processed_data)
    print(f"生成了 {len(signals)} 个信号")
    
    if not signals:
        print("没有生成任何信号！")
        return
    
    # 显示信号详情
    print("\n信号序列:")
    for i, signal in enumerate(signals):
        print(f"  {i+1:2d}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type:4s} "
              f"¥{float(signal.price):7.2f} - {signal.reason}")
    
    # 模拟交易执行过程
    print(f"\n=== 模拟交易执行过程 ===")
    
    capital = Decimal('100000')
    position = 0
    buy_price = None
    trades = []
    
    signal_dict = {signal.date: signal for signal in signals}
    
    print(f"初始资金: ¥{float(capital):,.2f}")
    print(f"止损阈值: 8%")
    print(f"止盈阈值: 20%")
    print(f"手续费率: 0.1%")
    
    for i, data_point in enumerate(processed_data):
        # 检查止损止盈
        if position > 0 and buy_price is not None:
            price_change = (data_point.close - buy_price) / buy_price
            
            # 检查止盈
            if price_change >= Decimal('0.20'):
                print(f"  {data_point.date.strftime('%Y-%m-%d')}: 触发止盈 (涨幅: {price_change:.2%})")
                proceeds = position * data_point.close * Decimal('0.999')
                capital += proceeds
                trades.append(f"止盈卖出 {position}股 @ ¥{float(data_point.close):.2f}")
                position = 0
                buy_price = None
                continue
            
            # 检查止损
            elif price_change <= Decimal('-0.08'):
                print(f"  {data_point.date.strftime('%Y-%m-%d')}: 触发止损 (跌幅: {price_change:.2%})")
                proceeds = position * data_point.close * Decimal('0.999')
                capital += proceeds
                trades.append(f"止损卖出 {position}股 @ ¥{float(data_point.close):.2f}")
                position = 0
                buy_price = None
                continue
        
        # 处理信号
        if data_point.date in signal_dict:
            signal = signal_dict[data_point.date]
            print(f"  {data_point.date.strftime('%Y-%m-%d')}: 收到 {signal.signal_type} 信号 @ ¥{float(signal.price):.2f}")
            print(f"    当前状态: 资金=¥{float(capital):,.2f}, 持仓={position}股")
            
            if signal.signal_type == 'BUY':
                if position == 0 and capital > 0:
                    # 可以买入
                    max_shares = int(capital / (signal.price * Decimal('1.001')))
                    if max_shares > 0:
                        cost = max_shares * signal.price * Decimal('1.001')
                        capital -= cost
                        position = max_shares
                        buy_price = signal.price
                        trades.append(f"买入 {max_shares}股 @ ¥{float(signal.price):.2f}")
                        print(f"    ✓ 执行买入: {max_shares}股, 成本: ¥{float(cost):,.2f}")
                    else:
                        print(f"    ✗ 买入失败: 资金不足")
                elif position > 0:
                    print(f"    ✗ 买入失败: 已有持仓 {position}股")
                else:
                    print(f"    ✗ 买入失败: 资金不足")
            
            elif signal.signal_type == 'SELL':
                if position > 0:
                    # 可以卖出
                    proceeds = position * signal.price * Decimal('0.999')
                    capital += proceeds
                    trades.append(f"卖出 {position}股 @ ¥{float(signal.price):.2f}")
                    print(f"    ✓ 执行卖出: {position}股, 收入: ¥{float(proceeds):,.2f}")
                    position = 0
                    buy_price = None
                else:
                    print(f"    ✗ 卖出失败: 无持仓")
    
    # 最终结果
    final_value = capital + (position * processed_data[-1].close if position > 0 else 0)
    total_return = (final_value - Decimal('100000')) / Decimal('100000')
    
    print(f"\n=== 最终结果 ===")
    print(f"最终资金: ¥{float(capital):,.2f}")
    print(f"最终持仓: {position}股")
    print(f"最终价值: ¥{float(final_value):,.2f}")
    print(f"总收益率: {float(total_return):.2%}")
    print(f"执行的交易: {len(trades)}次")
    
    if trades:
        print(f"\n交易记录:")
        for i, trade in enumerate(trades):
            print(f"  {i+1}. {trade}")
    else:
        print(f"\n❌ 没有执行任何交易！")
        
        # 分析原因
        print(f"\n可能的原因分析:")
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        
        if len(sell_signals) > 0 and len(buy_signals) == 0:
            print(f"- 只有卖出信号({len(sell_signals)}个)，没有买入信号")
        elif signals[0].signal_type == 'SELL':
            print(f"- 第一个信号是卖出信号，但初始没有持仓")
        elif len(buy_signals) > 0:
            print(f"- 有{len(buy_signals)}个买入信号，但可能被其他条件阻止")
        
        print(f"- 建议：检查信号生成逻辑，确保信号序列合理")


if __name__ == "__main__":
    analyze_signal_execution()
