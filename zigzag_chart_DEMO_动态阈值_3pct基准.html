
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ZigZag算法可视化 - DEMO - 动态阈值(3%基准)</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chart-container {
            width: 100%;
            height: 800px;
            padding: 20px;
            box-sizing: border-box;
        }
        .info {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .info h3 {
            margin-top: 0;
            color: #495057;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ZigZag算法可视化分析</h1>
            <h2>DEMO - 动态阈值(3%基准)</h2>
            <p>包含动态阈值的ZigZag算法，支持峰谷点标注和趋势线分析</p>
        </div>

        <div class="chart-container">
            <div id="chart" style="width: 100%; height: 100%;"></div>
        </div>

        <div class="info">
            <h3>📊 统计信息</h3>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-label">数据点数量</div>
                    <div class="stat-value">200</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">ZigZag转折点</div>
                    <div class="stat-value">112</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">峰点数量</div>
                    <div class="stat-value">56</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">谷点数量</div>
                    <div class="stat-value">56</div>
                </div>
            </div>

            <h3>🎯 使用说明</h3>
            <ul>
                <li><strong>峰谷点标注</strong>: 红色三角形表示峰点，绿色倒三角形表示谷点</li>
                <li><strong>鼠标悬停</strong>: 将鼠标移到峰谷点上可查看详细信息</li>
                <li><strong>趋势线分析</strong>: 点击峰谷点可显示相关的峰峰连线和谷谷连线</li>
                <li><strong>交叉点识别</strong>: 系统会自动识别并标注重要的趋势线交叉点</li>
                <li><strong>缩放平移</strong>: 支持鼠标滚轮缩放和拖拽平移</li>
            </ul>
        </div>
    </div>

    <script>
        // 图表数据
        const klineData = [["2024-01-01", 88.16, 88.99, 88.15, 89.81, 899702], ["2024-01-02", 100.58, 100.54, 100.36, 102.24, 1253605], ["2024-01-03", 100.88, 100.49, 98.79, 102.2, 1285190], ["2024-01-04", 98.23, 98.83, 97.1, 98.98, 1166196], ["2024-01-05", 104.22, 104.26, 103.47, 104.38, 1361095], ["2024-01-06", 100.65, 100.32, 98.57, 101.15, 1228055], ["2024-01-07", 103.27, 103.36, 103.14, 105.17, 1207547], ["2024-01-08", 99.91, 100.52, 98.45, 102.48, 917497], ["2024-01-09", 104.82, 103.89, 102.38, 105.9, 828682], ["2024-01-10", 102.31, 101.64, 101.12, 103.26, 1288783], ["2024-01-11", 107.35, 106.43, 106.32, 108.43, 867063], ["2024-01-12", 102.84, 103.29, 101.59, 104.59, 997736], ["2024-01-13", 105.19, 106.11, 104.76, 107.84, 1323204], ["2024-01-14", 105.17, 105.03, 104.34, 105.81, 1468697], ["2024-01-15", 108.2, 108.29, 106.67, 109.25, 1326948], ["2024-01-16", 109.42, 108.5, 106.63, 111.22, 1148940], ["2024-01-17", 104.62, 105.28, 103.26, 107.23, 1370821], ["2024-01-18", 109.38, 108.66, 107.6, 110.49, 1269853], ["2024-01-19", 109.13, 109.76, 108.1, 111.58, 863727], ["2024-01-20", 111.22, 110.68, 110.26, 112.4, 1288973], ["2024-01-21", 106.49, 107.29, 105.52, 108.23, 1186089], ["2024-01-22", 112.08, 112.25, 111.43, 113.46, 921663], ["2024-01-23", 110.32, 109.96, 108.5, 112.13, 1361018], ["2024-01-24", 109.13, 109.15, 107.63, 110.8, 1043155], ["2024-01-25", 110.62, 109.65, 108.09, 111.25, 1013524], ["2024-01-26", 111.52, 110.58, 109.78, 112.5, 959426], ["2024-01-27", 111.79, 112.32, 111.03, 114.03, 1009318], ["2024-01-28", 110.4, 110.46, 108.72, 111.61, 1144660], ["2024-01-29", 113.7, 113.01, 112.91, 114.87, 809953], ["2024-01-30", 115.08, 115.67, 114.36, 116.6, 1461048], ["2024-01-31", 117.01, 117.26, 115.5, 117.49, 1445439], ["2024-02-01", 113.92, 113.08, 111.88, 115.5, 1258572], ["2024-02-02", 113.05, 113.68, 112.38, 115.16, 1362526], ["2024-02-03", 112.21, 112.7, 110.41, 113.33, 1121134], ["2024-02-04", 118.11, 118.54, 117.67, 120.52, 1015996], ["2024-02-05", 117.24, 117.19, 115.34, 117.48, 1197687], ["2024-02-06", 113.48, 114.34, 113.1, 115.94, 1216801], ["2024-02-07", 120.64, 119.68, 118.88, 122.06, 865645], ["2024-02-08", 120.17, 120.17, 118.99, 121.85, 1215804], ["2024-02-09", 117.33, 117.65, 115.98, 119.44, 1188805], ["2024-02-10", 118.4, 118.48, 118.11, 118.79, 1332752], ["2024-02-11", 120.42, 120.29, 119.81, 121.3, 1195675], ["2024-02-12", 117.15, 117.75, 115.32, 118.83, 954328], ["2024-02-13", 122.55, 121.6, 121.12, 123.57, 821470], ["2024-02-14", 122.56, 122.76, 122.32, 123.49, 1340369], ["2024-02-15", 120.04, 119.57, 119.35, 121.46, 1380898], ["2024-02-16", 118.19, 118.87, 116.28, 120.63, 817740], ["2024-02-17", 119.23, 119.34, 117.32, 119.6, 1475163], ["2024-02-18", 122.69, 123.6, 122.49, 123.63, 971453], ["2024-02-19", 122.87, 123.23, 120.93, 124.33, 947196], ["2024-02-20", 122.08, 121.79, 119.84, 122.37, 1305687], ["2024-02-21", 123.51, 124.19, 121.75, 126.13, 1070238], ["2024-02-22", 125.46, 125.03, 124.99, 126.06, 1212807], ["2024-02-23", 121.65, 121.57, 119.63, 123.32, 893497], ["2024-02-24", 122.8, 122.53, 122.41, 122.9, 1346077], ["2024-02-25", 121.08, 121.21, 120.41, 122.58, 1382765], ["2024-02-26", 120.65, 121.59, 120.57, 122.67, 1099371], ["2024-02-27", 121.81, 121.08, 119.71, 121.9, 818422], ["2024-02-28", 123.9, 124.85, 123.68, 125.39, 1244123], ["2024-02-29", 122.78, 122.73, 122.68, 122.95, 1089057], ["2024-03-01", 124.45, 124.05, 122.67, 125.31, 1005767], ["2024-03-02", 122.81, 123.59, 121.64, 124.7, 1114454], ["2024-03-03", 122.89, 122.25, 121.69, 124.48, 1130527], ["2024-03-04", 123.98, 123.82, 122.12, 125.68, 970565], ["2024-03-05", 123.92, 124.57, 122.19, 125.08, 1479261], ["2024-03-06", 129.17, 128.44, 127.5, 129.43, 826271], ["2024-03-07", 127.68, 128.29, 125.73, 128.46, 1206054], ["2024-03-08", 128.94, 128.43, 127.78, 129.55, 1387084], ["2024-03-09", 126.67, 125.96, 124.2, 128.18, 1392114], ["2024-03-10", 124.69, 124.85, 122.69, 125.67, 1279743], ["2024-03-11", 128.9, 129.71, 128.11, 129.78, 1283300], ["2024-03-12", 127.65, 126.69, 126.44, 129.6, 1474465], ["2024-03-13", 125.55, 125.54, 124.95, 127.42, 1140718], ["2024-03-14", 127.53, 127.92, 125.79, 128.48, 978837], ["2024-03-15", 124.76, 124.79, 124.53, 126.45, 1371359], ["2024-03-16", 128.52, 129.03, 126.74, 129.21, 1376250], ["2024-03-17", 125.2, 124.77, 124.43, 125.25, 1414242], ["2024-03-18", 125.43, 125.19, 124.63, 127.3, 1068325], ["2024-03-19", 125.45, 125.38, 123.58, 126.45, 1315535], ["2024-03-20", 126.75, 126.17, 124.41, 127.87, 945876], ["2024-03-21", 127.43, 128.27, 125.43, 129.27, 896691], ["2024-03-22", 128.59, 128.28, 127.99, 129.46, 1498785], ["2024-03-23", 129.73, 128.98, 127.78, 130.78, 890465], ["2024-03-24", 129.68, 129.07, 127.28, 129.94, 1382984], ["2024-03-25", 127.71, 127.48, 127.14, 128.58, 1292326], ["2024-03-26", 126.37, 125.41, 124.13, 128.29, 1263470], ["2024-03-27", 128.0, 127.49, 127.27, 128.53, 803877], ["2024-03-28", 128.64, 128.4, 127.29, 129.72, 1211347], ["2024-03-29", 127.7, 127.13, 125.69, 128.96, 963277], ["2024-03-30", 131.73, 131.37, 130.69, 132.18, 1192386], ["2024-03-31", 127.41, 126.7, 126.27, 128.33, 1196887], ["2024-04-01", 129.01, 128.9, 127.77, 130.9, 866430], ["2024-04-02", 129.84, 130.59, 129.65, 131.22, 1498022], ["2024-04-03", 125.98, 126.36, 124.65, 127.96, 952261], ["2024-04-04", 129.08, 129.99, 128.6, 131.38, 1221210], ["2024-04-05", 126.43, 127.07, 126.29, 127.54, 1083772], ["2024-04-06", 126.99, 127.12, 125.97, 128.63, 1386434], ["2024-04-07", 125.58, 125.53, 124.85, 125.92, 1123827], ["2024-04-08", 129.91, 130.5, 128.32, 132.43, 861699], ["2024-04-09", 125.34, 125.55, 123.81, 127.08, 1367570], ["2024-04-10", 117.67, 118.02, 116.18, 118.74, 1327092], ["2024-04-11", 129.56, 129.91, 129.44, 131.5, 1371548], ["2024-04-12", 130.58, 129.97, 129.43, 131.1, 1223907], ["2024-04-13", 127.59, 127.4, 126.51, 128.26, 1265764], ["2024-04-14", 130.57, 130.73, 130.48, 132.39, 1342340], ["2024-04-15", 127.84, 127.51, 127.02, 128.23, 1191375], ["2024-04-16", 128.72, 128.46, 128.09, 129.46, 1401824], ["2024-04-17", 125.5, 126.38, 123.5, 126.48, 1099398], ["2024-04-18", 130.84, 129.91, 127.94, 131.98, 1366679], ["2024-04-19", 125.1, 124.61, 124.07, 126.66, 884375], ["2024-04-20", 124.76, 125.53, 123.5, 127.26, 926778], ["2024-04-21", 129.97, 129.72, 129.51, 130.52, 1393500], ["2024-04-22", 126.13, 126.69, 124.77, 127.57, 986900], ["2024-04-23", 127.59, 127.45, 127.43, 128.25, 1079110], ["2024-04-24", 127.2, 126.9, 126.47, 128.2, 1377739], ["2024-04-25", 123.53, 123.68, 121.58, 125.02, 930072], ["2024-04-26", 128.03, 128.1, 126.81, 129.52, 1393722], ["2024-04-27", 125.18, 126.12, 125.1, 127.7, 1228755], ["2024-04-28", 124.8, 124.87, 124.36, 126.44, 1105137], ["2024-04-29", 125.47, 126.02, 124.14, 126.61, 1439605], ["2024-04-30", 123.96, 124.06, 122.15, 124.82, 863908], ["2024-05-01", 127.85, 127.31, 125.75, 129.11, 1447611], ["2024-05-02", 121.7, 122.52, 120.35, 123.98, 1327053], ["2024-05-03", 124.51, 124.26, 122.62, 125.61, 1272458], ["2024-05-04", 126.05, 126.06, 125.54, 127.86, 1372194], ["2024-05-05", 126.07, 126.52, 125.22, 126.92, 1417919], ["2024-05-06", 121.49, 121.57, 120.87, 122.08, 1155804], ["2024-05-07", 123.65, 123.44, 121.89, 123.86, 1278592], ["2024-05-08", 124.03, 123.12, 121.47, 125.83, 1056429], ["2024-05-09", 124.72, 125.27, 122.89, 126.01, 809274], ["2024-05-10", 124.59, 125.12, 123.91, 125.73, 1131770], ["2024-05-11", 121.27, 122.05, 119.98, 123.25, 1080894], ["2024-05-12", 119.37, 120.02, 117.38, 120.65, 1036761], ["2024-05-13", 121.58, 120.81, 120.75, 122.82, 1087351], ["2024-05-14", 118.43, 119.41, 118.15, 119.84, 1449127], ["2024-05-15", 118.88, 119.24, 116.91, 119.83, 815363], ["2024-05-16", 122.64, 122.49, 121.14, 123.05, 966087], ["2024-05-17", 120.51, 120.45, 119.12, 121.49, 1119950], ["2024-05-18", 122.05, 121.58, 120.22, 123.04, 1335935], ["2024-05-19", 119.13, 119.07, 117.37, 119.68, 866566], ["2024-05-20", 120.03, 120.22, 119.32, 120.86, 1159128], ["2024-05-21", 120.32, 120.81, 118.46, 121.76, 992342], ["2024-05-22", 117.52, 118.24, 117.01, 120.08, 1010137], ["2024-05-23", 122.54, 122.73, 122.38, 124.3, 1419266], ["2024-05-24", 121.04, 121.67, 120.29, 121.97, 817995], ["2024-05-25", 119.74, 119.12, 117.28, 120.11, 1300462], ["2024-05-26", 122.29, 121.86, 120.75, 123.91, 1215411], ["2024-05-27", 118.18, 118.32, 117.74, 118.61, 1304246], ["2024-05-28", 118.63, 118.73, 118.08, 120.11, 1065768], ["2024-05-29", 117.78, 117.87, 117.2, 119.64, 1282652], ["2024-05-30", 109.7, 109.51, 108.38, 110.46, 1228929], ["2024-05-31", 116.19, 116.68, 115.1, 117.6, 1268685], ["2024-06-01", 119.37, 119.12, 118.83, 120.34, 1352284], ["2024-06-02", 114.02, 114.28, 113.95, 116.14, 1374586], ["2024-06-03", 116.51, 117.41, 116.37, 119.23, 1139403], ["2024-06-04", 117.16, 117.19, 116.51, 117.23, 839616], ["2024-06-05", 115.93, 115.85, 113.91, 117.67, 1013925], ["2024-06-06", 113.84, 113.03, 112.27, 115.76, 940551], ["2024-06-07", 115.5, 115.41, 115.33, 115.69, 1488615], ["2024-06-08", 116.54, 117.17, 114.67, 118.61, 1268929], ["2024-06-09", 113.75, 113.27, 112.38, 114.49, 851347], ["2024-06-10", 111.86, 112.06, 110.07, 112.74, 1196086], ["2024-06-11", 115.64, 116.45, 114.36, 116.9, 1039902], ["2024-06-12", 111.45, 112.22, 110.61, 113.67, 1250569], ["2024-06-13", 111.71, 111.43, 110.5, 112.53, 1392071], ["2024-06-14", 115.48, 115.39, 114.26, 115.83, 1084681], ["2024-06-15", 111.74, 112.44, 110.21, 114.08, 811022], ["2024-06-16", 113.65, 114.09, 112.88, 114.96, 1192908], ["2024-06-17", 113.32, 112.72, 112.18, 114.64, 833606], ["2024-06-18", 110.59, 110.41, 109.51, 112.17, 1492352], ["2024-06-19", 111.4, 112.2, 109.69, 112.9, 1210157], ["2024-06-20", 111.48, 111.43, 110.26, 112.51, 928038], ["2024-06-21", 112.4, 113.06, 110.99, 114.14, 1138110], ["2024-06-22", 108.33, 108.18, 107.18, 110.33, 1368635], ["2024-06-23", 114.13, 113.23, 111.97, 114.61, 1291967], ["2024-06-24", 112.04, 111.79, 110.59, 112.97, 1480298], ["2024-06-25", 109.54, 109.78, 109.45, 111.72, 862048], ["2024-06-26", 111.29, 111.07, 110.67, 113.15, 983194], ["2024-06-27", 107.2, 107.88, 105.2, 109.79, 1044538], ["2024-06-28", 112.11, 111.5, 109.52, 113.85, 1385534], ["2024-06-29", 110.23, 110.95, 108.67, 111.6, 977036], ["2024-06-30", 107.59, 108.02, 106.3, 109.56, 1158703], ["2024-07-01", 110.75, 110.45, 109.63, 111.49, 1227711], ["2024-07-02", 109.45, 108.96, 107.22, 111.26, 1072977], ["2024-07-03", 108.14, 107.82, 105.89, 108.77, 955964], ["2024-07-04", 107.65, 107.13, 105.44, 109.12, 1296064], ["2024-07-05", 106.03, 105.43, 104.57, 107.79, 1100008], ["2024-07-06", 108.56, 108.3, 107.65, 110.15, 1042402], ["2024-07-07", 109.37, 108.63, 108.06, 109.97, 921715], ["2024-07-08", 108.01, 107.27, 106.44, 109.82, 946858], ["2024-07-09", 107.45, 107.76, 106.96, 108.11, 1289130], ["2024-07-10", 106.71, 107.28, 106.25, 108.0, 1285952], ["2024-07-11", 108.06, 107.74, 106.04, 109.72, 851546], ["2024-07-12", 104.83, 103.95, 102.79, 106.42, 950076], ["2024-07-13", 106.79, 106.18, 104.91, 108.49, 812169], ["2024-07-14", 103.07, 103.77, 102.91, 105.76, 1240631], ["2024-07-15", 105.73, 105.88, 104.68, 105.96, 1313998], ["2024-07-16", 105.75, 106.32, 105.46, 106.6, 1167080], ["2024-07-17", 105.37, 106.02, 103.73, 106.86, 1487535], ["2024-07-18", 104.38, 103.64, 103.19, 104.51, 1134949]];
        const zigzagData = [{"name": "HIGH_1", "coord": ["2024-01-02", 102.24], "value": 102.24, "type": "HIGH", "index": 1}, {"name": "LOW_3", "coord": ["2024-01-04", 97.1], "value": 97.1, "type": "LOW", "index": 3}, {"name": "HIGH_4", "coord": ["2024-01-05", 104.38], "value": 104.38, "type": "HIGH", "index": 4}, {"name": "LOW_5", "coord": ["2024-01-06", 98.57], "value": 98.57, "type": "LOW", "index": 5}, {"name": "HIGH_6", "coord": ["2024-01-07", 105.17], "value": 105.17, "type": "HIGH", "index": 6}, {"name": "LOW_7", "coord": ["2024-01-08", 98.45], "value": 98.45, "type": "LOW", "index": 7}, {"name": "HIGH_8", "coord": ["2024-01-09", 105.9], "value": 105.9, "type": "HIGH", "index": 8}, {"name": "LOW_9", "coord": ["2024-01-10", 101.12], "value": 101.12, "type": "LOW", "index": 9}, {"name": "HIGH_10", "coord": ["2024-01-11", 108.43], "value": 108.43, "type": "HIGH", "index": 10}, {"name": "LOW_11", "coord": ["2024-01-12", 101.59], "value": 101.59, "type": "LOW", "index": 11}, {"name": "HIGH_15", "coord": ["2024-01-16", 111.22], "value": 111.22, "type": "HIGH", "index": 15}, {"name": "LOW_16", "coord": ["2024-01-17", 103.26], "value": 103.26, "type": "LOW", "index": 16}, {"name": "HIGH_19", "coord": ["2024-01-20", 112.4], "value": 112.4, "type": "HIGH", "index": 19}, {"name": "LOW_20", "coord": ["2024-01-21", 105.52], "value": 105.52, "type": "LOW", "index": 20}, {"name": "HIGH_21", "coord": ["2024-01-22", 113.46], "value": 113.46, "type": "HIGH", "index": 21}, {"name": "LOW_23", "coord": ["2024-01-24", 107.63], "value": 107.63, "type": "LOW", "index": 23}, {"name": "HIGH_26", "coord": ["2024-01-27", 114.03], "value": 114.03, "type": "HIGH", "index": 26}, {"name": "LOW_27", "coord": ["2024-01-28", 108.72], "value": 108.72, "type": "LOW", "index": 27}, {"name": "HIGH_30", "coord": ["2024-01-31", 117.49], "value": 117.49, "type": "HIGH", "index": 30}, {"name": "LOW_33", "coord": ["2024-02-03", 110.41], "value": 110.41, "type": "LOW", "index": 33}, {"name": "HIGH_34", "coord": ["2024-02-04", 120.52], "value": 120.52, "type": "HIGH", "index": 34}, {"name": "LOW_36", "coord": ["2024-02-06", 113.1], "value": 113.1, "type": "LOW", "index": 36}, {"name": "HIGH_37", "coord": ["2024-02-07", 122.06], "value": 122.06, "type": "HIGH", "index": 37}, {"name": "LOW_39", "coord": ["2024-02-09", 115.98], "value": 115.98, "type": "LOW", "index": 39}, {"name": "HIGH_41", "coord": ["2024-02-11", 121.3], "value": 121.3, "type": "HIGH", "index": 41}, {"name": "LOW_42", "coord": ["2024-02-12", 115.32], "value": 115.32, "type": "LOW", "index": 42}, {"name": "HIGH_43", "coord": ["2024-02-13", 123.57], "value": 123.57, "type": "HIGH", "index": 43}, {"name": "LOW_46", "coord": ["2024-02-16", 116.28], "value": 116.28, "type": "LOW", "index": 46}, {"name": "HIGH_49", "coord": ["2024-02-19", 124.33], "value": 124.33, "type": "HIGH", "index": 49}, {"name": "LOW_50", "coord": ["2024-02-20", 119.84], "value": 119.84, "type": "LOW", "index": 50}, {"name": "HIGH_51", "coord": ["2024-02-21", 126.13], "value": 126.13, "type": "HIGH", "index": 51}, {"name": "LOW_53", "coord": ["2024-02-23", 119.63], "value": 119.63, "type": "LOW", "index": 53}, {"name": "HIGH_58", "coord": ["2024-02-28", 125.39], "value": 125.39, "type": "HIGH", "index": 58}, {"name": "LOW_61", "coord": ["2024-03-02", 121.64], "value": 121.64, "type": "LOW", "index": 61}, {"name": "HIGH_63", "coord": ["2024-03-04", 125.68], "value": 125.68, "type": "HIGH", "index": 63}, {"name": "LOW_64", "coord": ["2024-03-05", 122.19], "value": 122.19, "type": "LOW", "index": 64}, {"name": "HIGH_65", "coord": ["2024-03-06", 129.43], "value": 129.43, "type": "HIGH", "index": 65}, {"name": "LOW_66", "coord": ["2024-03-07", 125.73], "value": 125.73, "type": "LOW", "index": 66}, {"name": "HIGH_67", "coord": ["2024-03-08", 129.55], "value": 129.55, "type": "HIGH", "index": 67}, {"name": "LOW_69", "coord": ["2024-03-10", 122.69], "value": 122.69, "type": "LOW", "index": 69}, {"name": "HIGH_70", "coord": ["2024-03-11", 129.78], "value": 129.78, "type": "HIGH", "index": 70}, {"name": "LOW_72", "coord": ["2024-03-13", 124.95], "value": 124.95, "type": "LOW", "index": 72}, {"name": "HIGH_73", "coord": ["2024-03-14", 128.48], "value": 128.48, "type": "HIGH", "index": 73}, {"name": "LOW_74", "coord": ["2024-03-15", 124.53], "value": 124.53, "type": "LOW", "index": 74}, {"name": "HIGH_75", "coord": ["2024-03-16", 129.21], "value": 129.21, "type": "HIGH", "index": 75}, {"name": "LOW_78", "coord": ["2024-03-19", 123.58], "value": 123.58, "type": "LOW", "index": 78}, {"name": "HIGH_82", "coord": ["2024-03-23", 130.78], "value": 130.78, "type": "HIGH", "index": 82}, {"name": "LOW_85", "coord": ["2024-03-26", 124.13], "value": 124.13, "type": "LOW", "index": 85}, {"name": "HIGH_87", "coord": ["2024-03-28", 129.72], "value": 129.72, "type": "HIGH", "index": 87}, {"name": "LOW_88", "coord": ["2024-03-29", 125.69], "value": 125.69, "type": "LOW", "index": 88}, {"name": "HIGH_89", "coord": ["2024-03-30", 132.18], "value": 132.18, "type": "HIGH", "index": 89}, {"name": "LOW_90", "coord": ["2024-03-31", 126.27], "value": 126.27, "type": "LOW", "index": 90}, {"name": "HIGH_92", "coord": ["2024-04-02", 131.22], "value": 131.22, "type": "HIGH", "index": 92}, {"name": "LOW_93", "coord": ["2024-04-03", 124.65], "value": 124.65, "type": "LOW", "index": 93}, {"name": "HIGH_94", "coord": ["2024-04-04", 131.38], "value": 131.38, "type": "HIGH", "index": 94}, {"name": "LOW_97", "coord": ["2024-04-07", 124.85], "value": 124.85, "type": "LOW", "index": 97}, {"name": "HIGH_98", "coord": ["2024-04-08", 132.43], "value": 132.43, "type": "HIGH", "index": 98}, {"name": "LOW_100", "coord": ["2024-04-10", 116.18], "value": 116.18, "type": "LOW", "index": 100}, {"name": "HIGH_104", "coord": ["2024-04-14", 132.39], "value": 132.39, "type": "HIGH", "index": 104}, {"name": "LOW_107", "coord": ["2024-04-17", 123.5], "value": 123.5, "type": "LOW", "index": 107}, {"name": "HIGH_108", "coord": ["2024-04-18", 131.98], "value": 131.98, "type": "HIGH", "index": 108}, {"name": "LOW_110", "coord": ["2024-04-20", 123.5], "value": 123.5, "type": "LOW", "index": 110}, {"name": "HIGH_111", "coord": ["2024-04-21", 130.52], "value": 130.52, "type": "HIGH", "index": 111}, {"name": "LOW_115", "coord": ["2024-04-25", 121.58], "value": 121.58, "type": "LOW", "index": 115}, {"name": "HIGH_116", "coord": ["2024-04-26", 129.52], "value": 129.52, "type": "HIGH", "index": 116}, {"name": "LOW_120", "coord": ["2024-04-30", 122.15], "value": 122.15, "type": "LOW", "index": 120}, {"name": "HIGH_121", "coord": ["2024-05-01", 129.11], "value": 129.11, "type": "HIGH", "index": 121}, {"name": "LOW_122", "coord": ["2024-05-02", 120.35], "value": 120.35, "type": "LOW", "index": 122}, {"name": "HIGH_124", "coord": ["2024-05-04", 127.86], "value": 127.86, "type": "HIGH", "index": 124}, {"name": "LOW_126", "coord": ["2024-05-06", 120.87], "value": 120.87, "type": "LOW", "index": 126}, {"name": "HIGH_129", "coord": ["2024-05-09", 126.01], "value": 126.01, "type": "HIGH", "index": 129}, {"name": "LOW_132", "coord": ["2024-05-12", 117.38], "value": 117.38, "type": "LOW", "index": 132}, {"name": "HIGH_133", "coord": ["2024-05-13", 122.82], "value": 122.82, "type": "HIGH", "index": 133}, {"name": "LOW_135", "coord": ["2024-05-15", 116.91], "value": 116.91, "type": "LOW", "index": 135}, {"name": "HIGH_136", "coord": ["2024-05-16", 123.05], "value": 123.05, "type": "HIGH", "index": 136}, {"name": "LOW_137", "coord": ["2024-05-17", 119.12], "value": 119.12, "type": "LOW", "index": 137}, {"name": "HIGH_138", "coord": ["2024-05-18", 123.04], "value": 123.04, "type": "HIGH", "index": 138}, {"name": "LOW_139", "coord": ["2024-05-19", 117.37], "value": 117.37, "type": "LOW", "index": 139}, {"name": "HIGH_141", "coord": ["2024-05-21", 121.76], "value": 121.76, "type": "HIGH", "index": 141}, {"name": "LOW_142", "coord": ["2024-05-22", 117.01], "value": 117.01, "type": "LOW", "index": 142}, {"name": "HIGH_143", "coord": ["2024-05-23", 124.3], "value": 124.3, "type": "HIGH", "index": 143}, {"name": "LOW_145", "coord": ["2024-05-25", 117.28], "value": 117.28, "type": "LOW", "index": 145}, {"name": "HIGH_146", "coord": ["2024-05-26", 123.91], "value": 123.91, "type": "HIGH", "index": 146}, {"name": "LOW_150", "coord": ["2024-05-30", 108.38], "value": 108.38, "type": "LOW", "index": 150}, {"name": "HIGH_152", "coord": ["2024-06-01", 120.34], "value": 120.34, "type": "HIGH", "index": 152}, {"name": "LOW_153", "coord": ["2024-06-02", 113.95], "value": 113.95, "type": "LOW", "index": 153}, {"name": "HIGH_154", "coord": ["2024-06-03", 119.23], "value": 119.23, "type": "HIGH", "index": 154}, {"name": "LOW_157", "coord": ["2024-06-06", 112.27], "value": 112.27, "type": "LOW", "index": 157}, {"name": "HIGH_159", "coord": ["2024-06-08", 118.61], "value": 118.61, "type": "HIGH", "index": 159}, {"name": "LOW_161", "coord": ["2024-06-10", 110.07], "value": 110.07, "type": "LOW", "index": 161}, {"name": "HIGH_162", "coord": ["2024-06-11", 116.9], "value": 116.9, "type": "HIGH", "index": 162}, {"name": "LOW_164", "coord": ["2024-06-13", 110.5], "value": 110.5, "type": "LOW", "index": 164}, {"name": "HIGH_165", "coord": ["2024-06-14", 115.83], "value": 115.83, "type": "HIGH", "index": 165}, {"name": "LOW_166", "coord": ["2024-06-15", 110.21], "value": 110.21, "type": "LOW", "index": 166}, {"name": "HIGH_167", "coord": ["2024-06-16", 114.96], "value": 114.96, "type": "HIGH", "index": 167}, {"name": "LOW_169", "coord": ["2024-06-18", 109.51], "value": 109.51, "type": "LOW", "index": 169}, {"name": "HIGH_172", "coord": ["2024-06-21", 114.14], "value": 114.14, "type": "HIGH", "index": 172}, {"name": "LOW_173", "coord": ["2024-06-22", 107.18], "value": 107.18, "type": "LOW", "index": 173}, {"name": "HIGH_174", "coord": ["2024-06-23", 114.61], "value": 114.61, "type": "HIGH", "index": 174}, {"name": "LOW_176", "coord": ["2024-06-25", 109.45], "value": 109.45, "type": "LOW", "index": 176}, {"name": "HIGH_177", "coord": ["2024-06-26", 113.15], "value": 113.15, "type": "HIGH", "index": 177}, {"name": "LOW_178", "coord": ["2024-06-27", 105.2], "value": 105.2, "type": "LOW", "index": 178}, {"name": "HIGH_179", "coord": ["2024-06-28", 113.85], "value": 113.85, "type": "HIGH", "index": 179}, {"name": "LOW_181", "coord": ["2024-06-30", 106.3], "value": 106.3, "type": "LOW", "index": 181}, {"name": "HIGH_182", "coord": ["2024-07-01", 111.49], "value": 111.49, "type": "HIGH", "index": 182}, {"name": "LOW_186", "coord": ["2024-07-05", 104.57], "value": 104.57, "type": "LOW", "index": 186}, {"name": "HIGH_187", "coord": ["2024-07-06", 110.15], "value": 110.15, "type": "HIGH", "index": 187}, {"name": "LOW_193", "coord": ["2024-07-12", 102.79], "value": 102.79, "type": "LOW", "index": 193}, {"name": "HIGH_194", "coord": ["2024-07-13", 108.49], "value": 108.49, "type": "HIGH", "index": 194}, {"name": "LOW_195", "coord": ["2024-07-14", 102.91], "value": 102.91, "type": "LOW", "index": 195}, {"name": "HIGH_198", "coord": ["2024-07-17", 106.86], "value": 106.86, "type": "HIGH", "index": 198}, {"name": "LOW_199", "coord": ["2024-07-18", 103.19], "value": 103.19, "type": "LOW", "index": 199}];
        const peakPoints = [{"name": "HIGH_1", "coord": ["2024-01-02", 102.24], "value": 102.24, "type": "HIGH", "index": 1}, {"name": "HIGH_4", "coord": ["2024-01-05", 104.38], "value": 104.38, "type": "HIGH", "index": 4}, {"name": "HIGH_6", "coord": ["2024-01-07", 105.17], "value": 105.17, "type": "HIGH", "index": 6}, {"name": "HIGH_8", "coord": ["2024-01-09", 105.9], "value": 105.9, "type": "HIGH", "index": 8}, {"name": "HIGH_10", "coord": ["2024-01-11", 108.43], "value": 108.43, "type": "HIGH", "index": 10}, {"name": "HIGH_15", "coord": ["2024-01-16", 111.22], "value": 111.22, "type": "HIGH", "index": 15}, {"name": "HIGH_19", "coord": ["2024-01-20", 112.4], "value": 112.4, "type": "HIGH", "index": 19}, {"name": "HIGH_21", "coord": ["2024-01-22", 113.46], "value": 113.46, "type": "HIGH", "index": 21}, {"name": "HIGH_26", "coord": ["2024-01-27", 114.03], "value": 114.03, "type": "HIGH", "index": 26}, {"name": "HIGH_30", "coord": ["2024-01-31", 117.49], "value": 117.49, "type": "HIGH", "index": 30}, {"name": "HIGH_34", "coord": ["2024-02-04", 120.52], "value": 120.52, "type": "HIGH", "index": 34}, {"name": "HIGH_37", "coord": ["2024-02-07", 122.06], "value": 122.06, "type": "HIGH", "index": 37}, {"name": "HIGH_41", "coord": ["2024-02-11", 121.3], "value": 121.3, "type": "HIGH", "index": 41}, {"name": "HIGH_43", "coord": ["2024-02-13", 123.57], "value": 123.57, "type": "HIGH", "index": 43}, {"name": "HIGH_49", "coord": ["2024-02-19", 124.33], "value": 124.33, "type": "HIGH", "index": 49}, {"name": "HIGH_51", "coord": ["2024-02-21", 126.13], "value": 126.13, "type": "HIGH", "index": 51}, {"name": "HIGH_58", "coord": ["2024-02-28", 125.39], "value": 125.39, "type": "HIGH", "index": 58}, {"name": "HIGH_63", "coord": ["2024-03-04", 125.68], "value": 125.68, "type": "HIGH", "index": 63}, {"name": "HIGH_65", "coord": ["2024-03-06", 129.43], "value": 129.43, "type": "HIGH", "index": 65}, {"name": "HIGH_67", "coord": ["2024-03-08", 129.55], "value": 129.55, "type": "HIGH", "index": 67}, {"name": "HIGH_70", "coord": ["2024-03-11", 129.78], "value": 129.78, "type": "HIGH", "index": 70}, {"name": "HIGH_73", "coord": ["2024-03-14", 128.48], "value": 128.48, "type": "HIGH", "index": 73}, {"name": "HIGH_75", "coord": ["2024-03-16", 129.21], "value": 129.21, "type": "HIGH", "index": 75}, {"name": "HIGH_82", "coord": ["2024-03-23", 130.78], "value": 130.78, "type": "HIGH", "index": 82}, {"name": "HIGH_87", "coord": ["2024-03-28", 129.72], "value": 129.72, "type": "HIGH", "index": 87}, {"name": "HIGH_89", "coord": ["2024-03-30", 132.18], "value": 132.18, "type": "HIGH", "index": 89}, {"name": "HIGH_92", "coord": ["2024-04-02", 131.22], "value": 131.22, "type": "HIGH", "index": 92}, {"name": "HIGH_94", "coord": ["2024-04-04", 131.38], "value": 131.38, "type": "HIGH", "index": 94}, {"name": "HIGH_98", "coord": ["2024-04-08", 132.43], "value": 132.43, "type": "HIGH", "index": 98}, {"name": "HIGH_104", "coord": ["2024-04-14", 132.39], "value": 132.39, "type": "HIGH", "index": 104}, {"name": "HIGH_108", "coord": ["2024-04-18", 131.98], "value": 131.98, "type": "HIGH", "index": 108}, {"name": "HIGH_111", "coord": ["2024-04-21", 130.52], "value": 130.52, "type": "HIGH", "index": 111}, {"name": "HIGH_116", "coord": ["2024-04-26", 129.52], "value": 129.52, "type": "HIGH", "index": 116}, {"name": "HIGH_121", "coord": ["2024-05-01", 129.11], "value": 129.11, "type": "HIGH", "index": 121}, {"name": "HIGH_124", "coord": ["2024-05-04", 127.86], "value": 127.86, "type": "HIGH", "index": 124}, {"name": "HIGH_129", "coord": ["2024-05-09", 126.01], "value": 126.01, "type": "HIGH", "index": 129}, {"name": "HIGH_133", "coord": ["2024-05-13", 122.82], "value": 122.82, "type": "HIGH", "index": 133}, {"name": "HIGH_136", "coord": ["2024-05-16", 123.05], "value": 123.05, "type": "HIGH", "index": 136}, {"name": "HIGH_138", "coord": ["2024-05-18", 123.04], "value": 123.04, "type": "HIGH", "index": 138}, {"name": "HIGH_141", "coord": ["2024-05-21", 121.76], "value": 121.76, "type": "HIGH", "index": 141}, {"name": "HIGH_143", "coord": ["2024-05-23", 124.3], "value": 124.3, "type": "HIGH", "index": 143}, {"name": "HIGH_146", "coord": ["2024-05-26", 123.91], "value": 123.91, "type": "HIGH", "index": 146}, {"name": "HIGH_152", "coord": ["2024-06-01", 120.34], "value": 120.34, "type": "HIGH", "index": 152}, {"name": "HIGH_154", "coord": ["2024-06-03", 119.23], "value": 119.23, "type": "HIGH", "index": 154}, {"name": "HIGH_159", "coord": ["2024-06-08", 118.61], "value": 118.61, "type": "HIGH", "index": 159}, {"name": "HIGH_162", "coord": ["2024-06-11", 116.9], "value": 116.9, "type": "HIGH", "index": 162}, {"name": "HIGH_165", "coord": ["2024-06-14", 115.83], "value": 115.83, "type": "HIGH", "index": 165}, {"name": "HIGH_167", "coord": ["2024-06-16", 114.96], "value": 114.96, "type": "HIGH", "index": 167}, {"name": "HIGH_172", "coord": ["2024-06-21", 114.14], "value": 114.14, "type": "HIGH", "index": 172}, {"name": "HIGH_174", "coord": ["2024-06-23", 114.61], "value": 114.61, "type": "HIGH", "index": 174}, {"name": "HIGH_177", "coord": ["2024-06-26", 113.15], "value": 113.15, "type": "HIGH", "index": 177}, {"name": "HIGH_179", "coord": ["2024-06-28", 113.85], "value": 113.85, "type": "HIGH", "index": 179}, {"name": "HIGH_182", "coord": ["2024-07-01", 111.49], "value": 111.49, "type": "HIGH", "index": 182}, {"name": "HIGH_187", "coord": ["2024-07-06", 110.15], "value": 110.15, "type": "HIGH", "index": 187}, {"name": "HIGH_194", "coord": ["2024-07-13", 108.49], "value": 108.49, "type": "HIGH", "index": 194}, {"name": "HIGH_198", "coord": ["2024-07-17", 106.86], "value": 106.86, "type": "HIGH", "index": 198}];
        const valleyPoints = [{"name": "LOW_3", "coord": ["2024-01-04", 97.1], "value": 97.1, "type": "LOW", "index": 3}, {"name": "LOW_5", "coord": ["2024-01-06", 98.57], "value": 98.57, "type": "LOW", "index": 5}, {"name": "LOW_7", "coord": ["2024-01-08", 98.45], "value": 98.45, "type": "LOW", "index": 7}, {"name": "LOW_9", "coord": ["2024-01-10", 101.12], "value": 101.12, "type": "LOW", "index": 9}, {"name": "LOW_11", "coord": ["2024-01-12", 101.59], "value": 101.59, "type": "LOW", "index": 11}, {"name": "LOW_16", "coord": ["2024-01-17", 103.26], "value": 103.26, "type": "LOW", "index": 16}, {"name": "LOW_20", "coord": ["2024-01-21", 105.52], "value": 105.52, "type": "LOW", "index": 20}, {"name": "LOW_23", "coord": ["2024-01-24", 107.63], "value": 107.63, "type": "LOW", "index": 23}, {"name": "LOW_27", "coord": ["2024-01-28", 108.72], "value": 108.72, "type": "LOW", "index": 27}, {"name": "LOW_33", "coord": ["2024-02-03", 110.41], "value": 110.41, "type": "LOW", "index": 33}, {"name": "LOW_36", "coord": ["2024-02-06", 113.1], "value": 113.1, "type": "LOW", "index": 36}, {"name": "LOW_39", "coord": ["2024-02-09", 115.98], "value": 115.98, "type": "LOW", "index": 39}, {"name": "LOW_42", "coord": ["2024-02-12", 115.32], "value": 115.32, "type": "LOW", "index": 42}, {"name": "LOW_46", "coord": ["2024-02-16", 116.28], "value": 116.28, "type": "LOW", "index": 46}, {"name": "LOW_50", "coord": ["2024-02-20", 119.84], "value": 119.84, "type": "LOW", "index": 50}, {"name": "LOW_53", "coord": ["2024-02-23", 119.63], "value": 119.63, "type": "LOW", "index": 53}, {"name": "LOW_61", "coord": ["2024-03-02", 121.64], "value": 121.64, "type": "LOW", "index": 61}, {"name": "LOW_64", "coord": ["2024-03-05", 122.19], "value": 122.19, "type": "LOW", "index": 64}, {"name": "LOW_66", "coord": ["2024-03-07", 125.73], "value": 125.73, "type": "LOW", "index": 66}, {"name": "LOW_69", "coord": ["2024-03-10", 122.69], "value": 122.69, "type": "LOW", "index": 69}, {"name": "LOW_72", "coord": ["2024-03-13", 124.95], "value": 124.95, "type": "LOW", "index": 72}, {"name": "LOW_74", "coord": ["2024-03-15", 124.53], "value": 124.53, "type": "LOW", "index": 74}, {"name": "LOW_78", "coord": ["2024-03-19", 123.58], "value": 123.58, "type": "LOW", "index": 78}, {"name": "LOW_85", "coord": ["2024-03-26", 124.13], "value": 124.13, "type": "LOW", "index": 85}, {"name": "LOW_88", "coord": ["2024-03-29", 125.69], "value": 125.69, "type": "LOW", "index": 88}, {"name": "LOW_90", "coord": ["2024-03-31", 126.27], "value": 126.27, "type": "LOW", "index": 90}, {"name": "LOW_93", "coord": ["2024-04-03", 124.65], "value": 124.65, "type": "LOW", "index": 93}, {"name": "LOW_97", "coord": ["2024-04-07", 124.85], "value": 124.85, "type": "LOW", "index": 97}, {"name": "LOW_100", "coord": ["2024-04-10", 116.18], "value": 116.18, "type": "LOW", "index": 100}, {"name": "LOW_107", "coord": ["2024-04-17", 123.5], "value": 123.5, "type": "LOW", "index": 107}, {"name": "LOW_110", "coord": ["2024-04-20", 123.5], "value": 123.5, "type": "LOW", "index": 110}, {"name": "LOW_115", "coord": ["2024-04-25", 121.58], "value": 121.58, "type": "LOW", "index": 115}, {"name": "LOW_120", "coord": ["2024-04-30", 122.15], "value": 122.15, "type": "LOW", "index": 120}, {"name": "LOW_122", "coord": ["2024-05-02", 120.35], "value": 120.35, "type": "LOW", "index": 122}, {"name": "LOW_126", "coord": ["2024-05-06", 120.87], "value": 120.87, "type": "LOW", "index": 126}, {"name": "LOW_132", "coord": ["2024-05-12", 117.38], "value": 117.38, "type": "LOW", "index": 132}, {"name": "LOW_135", "coord": ["2024-05-15", 116.91], "value": 116.91, "type": "LOW", "index": 135}, {"name": "LOW_137", "coord": ["2024-05-17", 119.12], "value": 119.12, "type": "LOW", "index": 137}, {"name": "LOW_139", "coord": ["2024-05-19", 117.37], "value": 117.37, "type": "LOW", "index": 139}, {"name": "LOW_142", "coord": ["2024-05-22", 117.01], "value": 117.01, "type": "LOW", "index": 142}, {"name": "LOW_145", "coord": ["2024-05-25", 117.28], "value": 117.28, "type": "LOW", "index": 145}, {"name": "LOW_150", "coord": ["2024-05-30", 108.38], "value": 108.38, "type": "LOW", "index": 150}, {"name": "LOW_153", "coord": ["2024-06-02", 113.95], "value": 113.95, "type": "LOW", "index": 153}, {"name": "LOW_157", "coord": ["2024-06-06", 112.27], "value": 112.27, "type": "LOW", "index": 157}, {"name": "LOW_161", "coord": ["2024-06-10", 110.07], "value": 110.07, "type": "LOW", "index": 161}, {"name": "LOW_164", "coord": ["2024-06-13", 110.5], "value": 110.5, "type": "LOW", "index": 164}, {"name": "LOW_166", "coord": ["2024-06-15", 110.21], "value": 110.21, "type": "LOW", "index": 166}, {"name": "LOW_169", "coord": ["2024-06-18", 109.51], "value": 109.51, "type": "LOW", "index": 169}, {"name": "LOW_173", "coord": ["2024-06-22", 107.18], "value": 107.18, "type": "LOW", "index": 173}, {"name": "LOW_176", "coord": ["2024-06-25", 109.45], "value": 109.45, "type": "LOW", "index": 176}, {"name": "LOW_178", "coord": ["2024-06-27", 105.2], "value": 105.2, "type": "LOW", "index": 178}, {"name": "LOW_181", "coord": ["2024-06-30", 106.3], "value": 106.3, "type": "LOW", "index": 181}, {"name": "LOW_186", "coord": ["2024-07-05", 104.57], "value": 104.57, "type": "LOW", "index": 186}, {"name": "LOW_193", "coord": ["2024-07-12", 102.79], "value": 102.79, "type": "LOW", "index": 193}, {"name": "LOW_195", "coord": ["2024-07-14", 102.91], "value": 102.91, "type": "LOW", "index": 195}, {"name": "LOW_199", "coord": ["2024-07-18", 103.19], "value": 103.19, "type": "LOW", "index": 199}];
        const zigzagRaw = [[1, 102.24, "HIGH", "2024-01-02"], [3, 97.1, "LOW", "2024-01-04"], [4, 104.38, "HIGH", "2024-01-05"], [5, 98.57, "LOW", "2024-01-06"], [6, 105.17, "HIGH", "2024-01-07"], [7, 98.45, "LOW", "2024-01-08"], [8, 105.9, "HIGH", "2024-01-09"], [9, 101.12, "LOW", "2024-01-10"], [10, 108.43, "HIGH", "2024-01-11"], [11, 101.59, "LOW", "2024-01-12"], [15, 111.22, "HIGH", "2024-01-16"], [16, 103.26, "LOW", "2024-01-17"], [19, 112.4, "HIGH", "2024-01-20"], [20, 105.52, "LOW", "2024-01-21"], [21, 113.46, "HIGH", "2024-01-22"], [23, 107.63, "LOW", "2024-01-24"], [26, 114.03, "HIGH", "2024-01-27"], [27, 108.72, "LOW", "2024-01-28"], [30, 117.49, "HIGH", "2024-01-31"], [33, 110.41, "LOW", "2024-02-03"], [34, 120.52, "HIGH", "2024-02-04"], [36, 113.1, "LOW", "2024-02-06"], [37, 122.06, "HIGH", "2024-02-07"], [39, 115.98, "LOW", "2024-02-09"], [41, 121.3, "HIGH", "2024-02-11"], [42, 115.32, "LOW", "2024-02-12"], [43, 123.57, "HIGH", "2024-02-13"], [46, 116.28, "LOW", "2024-02-16"], [49, 124.33, "HIGH", "2024-02-19"], [50, 119.84, "LOW", "2024-02-20"], [51, 126.13, "HIGH", "2024-02-21"], [53, 119.63, "LOW", "2024-02-23"], [58, 125.39, "HIGH", "2024-02-28"], [61, 121.64, "LOW", "2024-03-02"], [63, 125.68, "HIGH", "2024-03-04"], [64, 122.19, "LOW", "2024-03-05"], [65, 129.43, "HIGH", "2024-03-06"], [66, 125.73, "LOW", "2024-03-07"], [67, 129.55, "HIGH", "2024-03-08"], [69, 122.69, "LOW", "2024-03-10"], [70, 129.78, "HIGH", "2024-03-11"], [72, 124.95, "LOW", "2024-03-13"], [73, 128.48, "HIGH", "2024-03-14"], [74, 124.53, "LOW", "2024-03-15"], [75, 129.21, "HIGH", "2024-03-16"], [78, 123.58, "LOW", "2024-03-19"], [82, 130.78, "HIGH", "2024-03-23"], [85, 124.13, "LOW", "2024-03-26"], [87, 129.72, "HIGH", "2024-03-28"], [88, 125.69, "LOW", "2024-03-29"], [89, 132.18, "HIGH", "2024-03-30"], [90, 126.27, "LOW", "2024-03-31"], [92, 131.22, "HIGH", "2024-04-02"], [93, 124.65, "LOW", "2024-04-03"], [94, 131.38, "HIGH", "2024-04-04"], [97, 124.85, "LOW", "2024-04-07"], [98, 132.43, "HIGH", "2024-04-08"], [100, 116.18, "LOW", "2024-04-10"], [104, 132.39, "HIGH", "2024-04-14"], [107, 123.5, "LOW", "2024-04-17"], [108, 131.98, "HIGH", "2024-04-18"], [110, 123.5, "LOW", "2024-04-20"], [111, 130.52, "HIGH", "2024-04-21"], [115, 121.58, "LOW", "2024-04-25"], [116, 129.52, "HIGH", "2024-04-26"], [120, 122.15, "LOW", "2024-04-30"], [121, 129.11, "HIGH", "2024-05-01"], [122, 120.35, "LOW", "2024-05-02"], [124, 127.86, "HIGH", "2024-05-04"], [126, 120.87, "LOW", "2024-05-06"], [129, 126.01, "HIGH", "2024-05-09"], [132, 117.38, "LOW", "2024-05-12"], [133, 122.82, "HIGH", "2024-05-13"], [135, 116.91, "LOW", "2024-05-15"], [136, 123.05, "HIGH", "2024-05-16"], [137, 119.12, "LOW", "2024-05-17"], [138, 123.04, "HIGH", "2024-05-18"], [139, 117.37, "LOW", "2024-05-19"], [141, 121.76, "HIGH", "2024-05-21"], [142, 117.01, "LOW", "2024-05-22"], [143, 124.3, "HIGH", "2024-05-23"], [145, 117.28, "LOW", "2024-05-25"], [146, 123.91, "HIGH", "2024-05-26"], [150, 108.38, "LOW", "2024-05-30"], [152, 120.34, "HIGH", "2024-06-01"], [153, 113.95, "LOW", "2024-06-02"], [154, 119.23, "HIGH", "2024-06-03"], [157, 112.27, "LOW", "2024-06-06"], [159, 118.61, "HIGH", "2024-06-08"], [161, 110.07, "LOW", "2024-06-10"], [162, 116.9, "HIGH", "2024-06-11"], [164, 110.5, "LOW", "2024-06-13"], [165, 115.83, "HIGH", "2024-06-14"], [166, 110.21, "LOW", "2024-06-15"], [167, 114.96, "HIGH", "2024-06-16"], [169, 109.51, "LOW", "2024-06-18"], [172, 114.14, "HIGH", "2024-06-21"], [173, 107.18, "LOW", "2024-06-22"], [174, 114.61, "HIGH", "2024-06-23"], [176, 109.45, "LOW", "2024-06-25"], [177, 113.15, "HIGH", "2024-06-26"], [178, 105.2, "LOW", "2024-06-27"], [179, 113.85, "HIGH", "2024-06-28"], [181, 106.3, "LOW", "2024-06-30"], [182, 111.49, "HIGH", "2024-07-01"], [186, 104.57, "LOW", "2024-07-05"], [187, 110.15, "HIGH", "2024-07-06"], [193, 102.79, "LOW", "2024-07-12"], [194, 108.49, "HIGH", "2024-07-13"], [195, 102.91, "LOW", "2024-07-14"], [198, 106.86, "HIGH", "2024-07-17"], [199, 103.19, "LOW", "2024-07-18"]];

        // 初始化图表
        const chart = echarts.init(document.getElementById('chart'));

        // 趋势线分析器
        class TrendLineAnalyzer {
            static findPeakToPeakLines(zigzagRaw, currentIndex, lookback = 5) {
                const peaks = zigzagRaw.filter(point => point[2] === 'HIGH' && point[0] <= currentIndex);
                if (peaks.length < 2) return [];

                const recentPeaks = peaks.slice(-Math.min(lookback, peaks.length));
                const lines = [];

                for (let i = 0; i < recentPeaks.length - 1; i++) {
                    for (let j = i + 1; j < recentPeaks.length; j++) {
                        const peak1 = recentPeaks[i];
                        const peak2 = recentPeaks[j];

                        lines.push({
                            type: 'peak_to_peak',
                            start: { index: peak1[0], price: peak1[1], date: peak1[3] },
                            end: { index: peak2[0], price: peak2[1], date: peak2[3] }
                        });
                    }
                }

                return lines;
            }

            static findValleyToValleyLines(zigzagRaw, currentIndex, lookback = 5) {
                const valleys = zigzagRaw.filter(point => point[2] === 'LOW' && point[0] <= currentIndex);
                if (valleys.length < 2) return [];

                const recentValleys = valleys.slice(-Math.min(lookback, valleys.length));
                const lines = [];

                for (let i = 0; i < recentValleys.length - 1; i++) {
                    for (let j = i + 1; j < recentValleys.length; j++) {
                        const valley1 = recentValleys[i];
                        const valley2 = recentValleys[j];

                        lines.push({
                            type: 'valley_to_valley',
                            start: { index: valley1[0], price: valley1[1], date: valley1[3] },
                            end: { index: valley2[0], price: valley2[1], date: valley2[3] }
                        });
                    }
                }

                return lines;
            }
        }

        // 图表配置
        const option = {
            title: {
                text: 'ZigZag算法可视化 - DEMO - 动态阈值(3%基准)',
                left: 'center',
                textStyle: {
                    color: '#333',
                    fontSize: 18
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    if (params && params.length > 0) {
                        const data = params[0];
                        if (data.seriesName === 'K线') {
                            const values = data.data;
                            return `
                                <div style="padding: 10px;">
                                    <div style="font-weight: bold; margin-bottom: 8px;">${values[0]}</div>
                                    <div>开盘: <span style="color: #666;">${values[1].toFixed(2)}</span></div>
                                    <div>收盘: <span style="color: #666;">${values[2].toFixed(2)}</span></div>
                                    <div>最低: <span style="color: #666;">${values[3].toFixed(2)}</span></div>
                                    <div>最高: <span style="color: #666;">${values[4].toFixed(2)}</span></div>
                                    <div>成交量: <span style="color: #666;">${values[5].toLocaleString()}</span></div>
                                </div>
                            `;
                        }
                    }
                    return '';
                }
            },
            legend: {
                data: ['K线', '峰点', '谷点'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: klineData.map(item => item[0]),
                scale: true,
                boundaryGap: false,
                axisLine: { onZero: false },
                splitLine: { show: false },
                min: 'dataMin',
                max: 'dataMax'
            },
            yAxis: {
                scale: true,
                splitArea: {
                    show: true
                }
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                },
                {
                    show: true,
                    type: 'slider',
                    top: '90%',
                    start: 0,
                    end: 100
                }
            ],
            series: [
                {
                    name: 'K线',
                    type: 'candlestick',
                    data: klineData.map(item => [item[1], item[2], item[3], item[4]]),
                    itemStyle: {
                        color: '#ef232a',
                        color0: '#14b143',
                        borderColor: '#ef232a',
                        borderColor0: '#14b143'
                    }
                },
                {
                    name: '峰点',
                    type: 'scatter',
                    coordinateSystem: 'cartesian2d',
                    data: peakPoints.map(point => [point.coord[0], point.coord[1]]),
                    symbol: 'triangle',
                    symbolSize: 12,
                    itemStyle: {
                        color: '#ff4757'
                    },
                    tooltip: {
                        formatter: function(params) {
                            const point = peakPoints[params.dataIndex];
                            return `
                                <div style="padding: 10px;">
                                    <div style="font-weight: bold; color: #ff4757; margin-bottom: 8px;">📈 峰点</div>
                                    <div>日期: <span style="color: #666;">${point.coord[0]}</span></div>
                                    <div>价格: <span style="color: #666;">${point.value.toFixed(2)}</span></div>
                                    <div>索引: <span style="color: #666;">${point.index}</span></div>
                                </div>
                            `;
                        }
                    }
                },
                {
                    name: '谷点',
                    type: 'scatter',
                    coordinateSystem: 'cartesian2d',
                    data: valleyPoints.map(point => [point.coord[0], point.coord[1]]),
                    symbol: 'triangle',
                    symbolRotate: 180,
                    symbolSize: 12,
                    itemStyle: {
                        color: '#2ed573'
                    },
                    tooltip: {
                        formatter: function(params) {
                            const point = valleyPoints[params.dataIndex];
                            return `
                                <div style="padding: 10px;">
                                    <div style="font-weight: bold; color: #2ed573; margin-bottom: 8px;">📉 谷点</div>
                                    <div>日期: <span style="color: #666;">${point.coord[0]}</span></div>
                                    <div>价格: <span style="color: #666;">${point.value.toFixed(2)}</span></div>
                                    <div>索引: <span style="color: #666;">${point.index}</span></div>
                                </div>
                            `;
                        }
                    }
                }
            ]
        };

        // 设置图表选项
        chart.setOption(option);

        // 响应式调整
        window.addEventListener('resize', function() {
            chart.resize();
        });

        // 点击事件处理 - 显示趋势线
        chart.on('click', function(params) {
            if (params.seriesName === '峰点' || params.seriesName === '谷点') {
                const currentIndex = params.seriesName === '峰点' ?
                    peakPoints[params.dataIndex].index :
                    valleyPoints[params.dataIndex].index;

                // 计算趋势线
                const peakLines = TrendLineAnalyzer.findPeakToPeakLines(zigzagRaw, currentIndex, 3);
                const valleyLines = TrendLineAnalyzer.findValleyToValleyLines(zigzagRaw, currentIndex, 3);

                // 添加趋势线到图表
                const newSeries = [...option.series];

                // 添加峰峰连线
                peakLines.forEach((line, index) => {
                    newSeries.push({
                        name: `峰峰连线${index + 1}`,
                        type: 'line',
                        data: [
                            [line.start.date, line.start.price],
                            [line.end.date, line.end.price]
                        ],
                        lineStyle: {
                            color: '#ff6b6b',
                            width: 2,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        animation: false
                    });
                });

                // 添加谷谷连线
                valleyLines.forEach((line, index) => {
                    newSeries.push({
                        name: `谷谷连线${index + 1}`,
                        type: 'line',
                        data: [
                            [line.start.date, line.start.price],
                            [line.end.date, line.end.price]
                        ],
                        lineStyle: {
                            color: '#4ecdc4',
                            width: 2,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        animation: false
                    });
                });

                // 更新图表
                chart.setOption({
                    series: newSeries
                });

                // 3秒后清除趋势线
                setTimeout(() => {
                    chart.setOption({
                        series: option.series
                    });
                }, 3000);
            }
        });

        console.log('ZigZag可视化图表已加载完成');
        console.log('数据统计:', {
            'K线数据点': klineData.length,
            'ZigZag转折点': zigzagData.length,
            '峰点': peakPoints.length,
            '谷点': valleyPoints.length
        });
    </script>
</body>
</html>
        