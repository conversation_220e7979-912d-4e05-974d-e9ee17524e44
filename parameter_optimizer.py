#!/usr/bin/env python3
"""
参数优化器 - 自动寻找最佳策略参数
支持网格搜索、随机搜索和贝叶斯优化
"""

import sys
import os
import itertools
import random
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed
import json
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest import get_stock_data, dataframe_to_stock_data
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from improved_backtest_engine import ImprovedBacktestEngine


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, stock_data: List, optimization_metric: str = 'total_return'):
        """
        初始化参数优化器
        
        Args:
            stock_data: 股票数据
            optimization_metric: 优化目标指标 ('total_return', 'sharpe_ratio', 'max_drawdown', 'profit_factor')
        """
        self.stock_data = stock_data
        self.optimization_metric = optimization_metric
        self.results = []
        
    def define_parameter_space(self) -> Dict[str, List]:
        """定义参数搜索空间"""
        return {
            # ZigZag参数
            'zigzag_threshold': [0.03, 0.05, 0.08, 0.10, 0.12],
            
            # Fibonacci水平
            'fib_levels': [
                [0.236, 0.382, 0.618],
                [0.236, 0.382, 0.5, 0.618],
                [0.236, 0.382, 0.5, 0.618, 0.786],
                [0.382, 0.5, 0.618],
                [0.236, 0.5, 0.786]
            ],
            
            # 过滤条件
            'volume_confirmation': [True, False],
            'rsi_filter': [True, False],
            'dynamic_threshold': [True, False],
            
            # 技术参数
            'min_bandwidth': [0.005, 0.01, 0.02, 0.03],
            'min_signal_interval': [1, 2, 3, 5],
            'volume_multiplier': [1.1, 1.2, 1.5, 2.0],
            'rsi_overbought': [70, 75, 80],
            'rsi_oversold': [20, 25, 30],
            
            # 回测参数
            'stop_loss_pct': [0.05, 0.08, 0.10, 0.12],
            'take_profit_pct': [0.15, 0.20, 0.25, 0.30],
            'commission_rate': [0.001, 0.002, 0.003]
        }
    
    def grid_search(self, max_combinations: int = 1000) -> List[Dict]:
        """网格搜索优化"""
        print(f"🔍 开始网格搜索优化...")
        
        param_space = self.define_parameter_space()
        
        # 生成所有参数组合
        param_names = list(param_space.keys())
        param_values = list(param_space.values())
        
        all_combinations = list(itertools.product(*param_values))
        
        # 如果组合太多，随机采样
        if len(all_combinations) > max_combinations:
            print(f"   参数组合过多({len(all_combinations)})，随机采样{max_combinations}个")
            all_combinations = random.sample(all_combinations, max_combinations)
        
        print(f"   将测试 {len(all_combinations)} 个参数组合")
        
        # 并行执行回测
        results = []
        with ProcessPoolExecutor(max_workers=4) as executor:
            futures = []
            
            for i, combination in enumerate(all_combinations):
                params = dict(zip(param_names, combination))
                future = executor.submit(self._single_backtest, params, i)
                futures.append(future)
            
            # 收集结果
            completed = 0
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                    completed += 1
                    
                    if completed % 50 == 0:
                        print(f"   已完成: {completed}/{len(all_combinations)}")
                        
                except Exception as e:
                    print(f"   回测失败: {e}")
        
        # 按优化指标排序
        results.sort(key=lambda x: x['metrics'][self.optimization_metric], reverse=True)
        self.results = results
        
        print(f"✅ 网格搜索完成，找到 {len(results)} 个有效结果")
        return results
    
    def random_search(self, n_trials: int = 500) -> List[Dict]:
        """随机搜索优化"""
        print(f"🎲 开始随机搜索优化 ({n_trials} 次试验)...")
        
        param_space = self.define_parameter_space()
        
        results = []
        with ProcessPoolExecutor(max_workers=4) as executor:
            futures = []
            
            for i in range(n_trials):
                # 随机选择参数
                params = {}
                for param_name, param_values in param_space.items():
                    params[param_name] = random.choice(param_values)
                
                future = executor.submit(self._single_backtest, params, i)
                futures.append(future)
            
            # 收集结果
            completed = 0
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                    completed += 1
                    
                    if completed % 50 == 0:
                        print(f"   已完成: {completed}/{n_trials}")
                        
                except Exception as e:
                    print(f"   回测失败: {e}")
        
        # 按优化指标排序
        results.sort(key=lambda x: x['metrics'][self.optimization_metric], reverse=True)
        self.results = results
        
        print(f"✅ 随机搜索完成，找到 {len(results)} 个有效结果")
        return results

    def bayesian_optimization(self, n_trials: int = 100) -> List[Dict]:
        """贝叶斯优化 (简化版本)"""
        print(f"🧠 开始贝叶斯优化 ({n_trials} 次试验)...")

        # 先进行少量随机搜索作为初始样本
        initial_samples = min(20, n_trials // 5)
        print(f"   初始随机采样: {initial_samples} 次")

        results = []
        param_space = self.define_parameter_space()

        # 初始随机采样
        for i in range(initial_samples):
            params = {}
            for param_name, param_values in param_space.items():
                params[param_name] = random.choice(param_values)

            result = self._single_backtest(params, i)
            if result:
                results.append(result)

        print(f"   初始采样完成，获得 {len(results)} 个有效样本")

        # 贝叶斯优化主循环
        for i in range(initial_samples, n_trials):
            # 基于历史结果选择下一个参数组合
            next_params = self._select_next_params(results, param_space)

            result = self._single_backtest(next_params, i)
            if result:
                results.append(result)

            if i % 20 == 0:
                print(f"   已完成: {i}/{n_trials}")

        # 按优化指标排序
        results.sort(key=lambda x: x['metrics'][self.optimization_metric], reverse=True)
        self.results = results

        print(f"✅ 贝叶斯优化完成，找到 {len(results)} 个有效结果")
        return results

    def _select_next_params(self, historical_results: List[Dict], param_space: Dict) -> Dict:
        """基于历史结果选择下一个参数组合 (简化的贝叶斯方法)"""
        if len(historical_results) < 5:
            # 样本太少，随机选择
            params = {}
            for param_name, param_values in param_space.items():
                params[param_name] = random.choice(param_values)
            return params

        # 分析最佳参数的分布
        top_results = sorted(historical_results,
                           key=lambda x: x['metrics'][self.optimization_metric],
                           reverse=True)[:5]

        # 基于最佳结果选择参数
        params = {}
        for param_name, param_values in param_space.items():
            # 统计最佳结果中各参数值的出现频率
            param_counts = {}
            for result in top_results:
                value = result['parameters'][param_name]
                # 将列表转换为字符串以便计数
                if isinstance(value, list):
                    value = str(value)
                param_counts[value] = param_counts.get(value, 0) + 1

            if param_counts:
                # 选择出现频率最高的参数值，但加入一些随机性
                if random.random() < 0.7:  # 70%概率选择最佳
                    best_value = max(param_counts.keys(), key=param_counts.get)
                    # 如果是字符串化的列表，需要转换回来
                    if best_value.startswith('['):
                        import ast
                        params[param_name] = ast.literal_eval(best_value)
                    else:
                        # 在param_values中找到对应的值
                        for pv in param_values:
                            if str(pv) == best_value:
                                params[param_name] = pv
                                break
                        else:
                            params[param_name] = random.choice(param_values)
                else:  # 30%概率随机探索
                    params[param_name] = random.choice(param_values)
            else:
                params[param_name] = random.choice(param_values)

        return params
    
    def _single_backtest(self, params: Dict, trial_id: int) -> Optional[Dict]:
        """执行单次回测"""
        try:
            # 创建策略
            strategy = ImprovedZigZagFibonacciSignalGenerator(
                zigzag_threshold=params['zigzag_threshold'],
                fib_levels=params['fib_levels'],
                dynamic_threshold=params['dynamic_threshold'],
                volume_confirmation=params['volume_confirmation'],
                rsi_filter=params['rsi_filter'],
                macd_filter=False,  # 固定为False以简化
                min_bandwidth=params['min_bandwidth'],
                min_signal_interval=params['min_signal_interval'],
                volume_multiplier=params['volume_multiplier'],
                rsi_overbought=params['rsi_overbought'],
                rsi_oversold=params['rsi_oversold']
            )
            
            # 创建回测引擎
            engine = ImprovedBacktestEngine(
                initial_capital=100000,
                commission_rate=params['commission_rate'],
                signal_generator=strategy,
                stop_loss_pct=params['stop_loss_pct'],
                take_profit_pct=params['take_profit_pct']
            )
            
            # 运行回测
            results = engine.run_backtest({'STOCK': self.stock_data})
            performance = results['STOCK']['performance']
            
            # 计算评估指标
            metrics = self._calculate_metrics(performance, results['STOCK']['data'])
            
            return {
                'trial_id': trial_id,
                'parameters': params,
                'metrics': metrics,
                'performance': performance
            }
            
        except Exception as e:
            print(f"   试验 {trial_id} 失败: {e}")
            return None
    
    def _calculate_metrics(self, performance: Dict, stock_data: List) -> Dict:
        """计算评估指标"""
        trades = performance['trades']
        initial_capital = float(performance['initial_capital'])
        final_value = float(performance['final_value'])
        
        # 基本指标
        total_return = float(performance['total_return'])
        
        # 计算买入持有收益率
        if stock_data:
            buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
        else:
            buy_hold_return = 0
        
        # 计算交易统计
        if trades:
            winning_trades = [t for t in trades if t['type'] in ['SELL', 'TAKE_PROFIT'] and 
                            ('proceeds' in t and t['proceeds'] > t.get('cost', 0))]
            total_trades = len([t for t in trades if t['type'] in ['BUY', 'SELL']])
            win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        else:
            win_rate = 0
            total_trades = 0
        
        # 计算最大回撤 (简化版本)
        max_drawdown = 0
        peak = initial_capital
        for trade in trades:
            if trade['type'] == 'SELL':
                current_value = float(trade.get('proceeds', 0))
                if current_value > peak:
                    peak = current_value
                drawdown = (peak - current_value) / peak
                max_drawdown = max(max_drawdown, drawdown)
        
        # 计算夏普比率 (简化版本)
        if total_return > 0:
            sharpe_ratio = total_return / max(0.01, max_drawdown)  # 简化计算
        else:
            sharpe_ratio = -1
        
        # 计算盈亏比
        if trades:
            profits = [float(t.get('proceeds', 0)) - float(t.get('cost', 0)) 
                      for t in trades if t['type'] in ['SELL', 'TAKE_PROFIT']]
            if profits:
                avg_profit = sum(p for p in profits if p > 0) / max(1, len([p for p in profits if p > 0]))
                avg_loss = abs(sum(p for p in profits if p < 0)) / max(1, len([p for p in profits if p < 0]))
                profit_factor = avg_profit / max(0.01, avg_loss)
            else:
                profit_factor = 0
        else:
            profit_factor = 0
        
        return {
            'total_return': total_return,
            'buy_hold_return': buy_hold_return,
            'excess_return': total_return - buy_hold_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profit_factor': profit_factor,
            'final_value': final_value
        }
    
    def get_best_parameters(self, top_n: int = 5) -> List[Dict]:
        """获取最佳参数"""
        if not self.results:
            print("❌ 没有优化结果，请先运行优化")
            return []
        
        return self.results[:top_n]
    
    def save_results(self, filename: str = None):
        """保存优化结果"""
        if not filename:
            filename = f"optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 准备保存的数据
        save_data = {
            'optimization_metric': self.optimization_metric,
            'total_trials': len(self.results),
            'timestamp': datetime.now().isoformat(),
            'results': []
        }
        
        for result in self.results:
            save_data['results'].append({
                'trial_id': result['trial_id'],
                'parameters': result['parameters'],
                'metrics': result['metrics']
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"💾 优化结果已保存到: {filename}")
    
    def print_best_results(self, top_n: int = 3):
        """打印最佳结果"""
        if not self.results:
            print("❌ 没有优化结果")
            return
        
        print(f"\n🏆 最佳 {top_n} 个参数组合 (按 {self.optimization_metric} 排序):")
        print("=" * 80)
        
        for i, result in enumerate(self.results[:top_n]):
            metrics = result['metrics']
            params = result['parameters']
            
            print(f"\n第 {i+1} 名:")
            print(f"  📊 {self.optimization_metric}: {metrics[self.optimization_metric]:.4f}")
            print(f"  💰 总收益率: {metrics['total_return']:.2%}")
            print(f"  📈 超额收益: {metrics['excess_return']:.2%}")
            print(f"  🎯 胜率: {metrics['win_rate']:.2%}")
            print(f"  📉 最大回撤: {metrics['max_drawdown']:.2%}")
            print(f"  🔄 交易次数: {metrics['total_trades']}")
            
            print(f"  ⚙️  关键参数:")
            print(f"     ZigZag阈值: {params['zigzag_threshold']:.1%}")
            print(f"     Fibonacci水平: {params['fib_levels']}")
            print(f"     止损: {params['stop_loss_pct']:.1%}")
            print(f"     止盈: {params['take_profit_pct']:.1%}")
            print(f"     成交量确认: {params['volume_confirmation']}")
            print(f"     RSI过滤: {params['rsi_filter']}")


def main():
    """主函数 - 演示参数优化"""
    print("🚀 参数优化演示")
    print("=" * 50)
    
    # 获取股票数据
    try:
        stock_code = "003021"
        start_date = "20200101"
        end_date = "20240101"
        
        print(f"📈 获取股票数据: {stock_code} ({start_date} - {end_date})")
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data = dataframe_to_stock_data(df, stock_code)
        print(f"✅ 获取到 {len(stock_data)} 个数据点")
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return
    
    # 创建优化器
    optimizer = ParameterOptimizer(stock_data, optimization_metric='total_return')
    
    # 选择优化方法
    print(f"\n🔧 选择优化方法:")
    print(f"1. 网格搜索 (全面但慢)")
    print(f"2. 随机搜索 (快速但可能遗漏最优解)")
    print(f"3. 贝叶斯优化 (智能搜索，推荐)")

    choice = input("请选择 (1/2/3): ").strip()

    if choice == '1':
        # 网格搜索
        results = optimizer.grid_search(max_combinations=200)
    elif choice == '2':
        # 随机搜索
        results = optimizer.random_search(n_trials=200)
    else:
        # 贝叶斯优化
        results = optimizer.bayesian_optimization(n_trials=150)
    
    # 显示结果
    optimizer.print_best_results(top_n=3)
    
    # 保存结果
    optimizer.save_results()
    
    print(f"\n✨ 优化完成！")


if __name__ == "__main__":
    main()
