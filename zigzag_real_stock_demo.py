#!/usr/bin/env python3
"""
ZigZag Fibonacci 策略真实股票数据演示

使用真实股票数据展示改造后的BacktestEngine和可视化功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest import get_stock_data, dataframe_to_stock_data
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 策略真实股票数据演示 ===\n")
    
    # 1. 获取真实股票数据
    print("1. 获取股票数据...")
    try:
        # 获取股票数据（你可以修改股票代码和日期范围）
        stock_code = "003021"  # 兆威机电
        start_date = "20160101"
        end_date = "20241201"
        
        print(f"   股票代码: {stock_code}")
        print(f"   时间范围: {start_date} - {end_date}")
        
        df = get_stock_data(stock_code, start_date, end_date)
        stock_data = dataframe_to_stock_data(df, stock_code)
        
        print(f"   获取数据点: {len(stock_data)}个")
        if stock_data:
            print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
        
    except Exception as e:
        print(f"   获取股票数据失败: {e}")
        print("   使用模拟数据代替...")
        # 如果获取真实数据失败，使用模拟数据
        stock_data = create_fallback_data()
        stock_code = "DEMO"
    
    if not stock_data:
        print("   没有可用的股票数据")
        return
    
    # 2. 初始化ZigZag Fibonacci策略
    print("\n2. 初始化ZigZag Fibonacci策略...")
    
    # 尝试不同的参数设置
    strategies = [
        {"threshold": 0.03, "name": "敏感策略(3%)"},
        {"threshold": 0.05, "name": "标准策略(5%)"},
        {"threshold": 0.08, "name": "保守策略(8%)"},
        {"threshold": 0.12, "name": "保守策略(12%)"}
    ]
    
    best_strategy = None
    best_return = -float('inf')
    best_results = None
    
    for strategy_config in strategies:
        print(f"\n   测试 {strategy_config['name']}...")
        
        zigzag_strategy = ZigZagFibonacciSignalGenerator(
            zigzag_threshold=strategy_config['threshold'],
            fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
        )
        
        # 初始化回测引擎（包含止损和止盈功能）
        backtest_engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            signal_generator=zigzag_strategy,
            stop_loss_pct=0.05,    # 5%止损
            take_profit_pct=0.15   # 15%止盈
        )
        
        # 运行回测
        stock_data_dict = {stock_code: stock_data}
        results = backtest_engine.run_backtest(stock_data_dict)
        
        # 提取结果
        stock_results = results[stock_code]
        signals = stock_results['signals']
        performance = stock_results['performance']
        zigzag_points = stock_results.get('zigzag_points', [])
        
        strategy_return = float(performance['total_return'])
        
        print(f"     ZigZag转折点: {len(zigzag_points)}个")
        print(f"     交易信号: {len(signals)}个")
        print(f"     收益率: {strategy_return:.2%}")
        
        # 记录最佳策略
        if strategy_return > best_return:
            best_return = strategy_return
            best_strategy = strategy_config
            best_results = stock_results
    
    if best_results is None:
        print("\n   所有策略都未产生有效结果")
        return
    
    print(f"\n   最佳策略: {best_strategy['name']} (收益率: {best_return:.2%})")
    
    # 3. 详细分析最佳策略
    print("\n3. 最佳策略详细分析:")
    signals = best_results['signals']
    trades = best_results['performance']['trades']
    zigzag_points = best_results.get('zigzag_points', [])
    performance = best_results['performance']
    
    print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"   总收益率: {float(performance['total_return']):.2%}")
    print(f"   交易次数: {len(trades)}")
    print(f"   止损次数: {performance.get('stop_loss_count', 0)}")
    print(f"   止盈次数: {performance.get('take_profit_count', 0)}")
    print(f"   止损设置: {performance.get('stop_loss_pct', 0):.1%}")
    print(f"   止盈设置: {performance.get('take_profit_pct', 0):.1%}")
    
    # 4. 显示交易信号
    if signals:
        print("\n4. 交易信号详情:")
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        print(f"   买入信号: {len(buy_signals)}个")
        print(f"   卖出信号: {len(sell_signals)}个")
        
        print("\n   信号列表:")
        for i, signal in enumerate(signals[:10]):  # 显示前10个信号
            print(f"   {i+1:2d}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):7.2f} - {signal.reason}")
        
        if len(signals) > 10:
            print(f"   ... 还有 {len(signals) - 10} 个信号")
    
    # 5. 显示ZigZag转折点
    if zigzag_points:
        print(f"\n5. ZigZag转折点 (共{len(zigzag_points)}个):")
        for i, (idx, price, point_type) in enumerate(zigzag_points[:8]):  # 显示前8个
            date = stock_data[idx].date
            print(f"   {i+1:2d}. {date.strftime('%Y-%m-%d')} ¥{float(price):7.2f} ({point_type})")
        
        if len(zigzag_points) > 8:
            print(f"   ... 还有 {len(zigzag_points) - 8} 个转折点")
    
    # 6. 生成可视化图表
    print("\n6. 生成可视化图表...")
    try:
        visualizer = StockChartVisualizer()
        
        # 创建包含ZigZag线的图表
        chart = visualizer.create_kline_chart(
            stock_data=best_results['data'],
            signals=signals,
            trades=trades,
            symbol=stock_code,
            zigzag_points=zigzag_points
        )
        
        # 保存图表
        filename = f"zigzag_{stock_code}_analysis.html"
        visualizer.save_chart_with_summary(chart, best_results, filename)
        print(f"   图表已保存到: {filename}")
        print("   图表包含:")
        print("   - K线图和成交量")
        print("   - 布林线（9天均线，1倍标准差）")
        print("   - ZigZag转折点标注（红色▲绿色▼）")
        print("   - ZigZag连线（红色虚线）")
        print("   - 交易信号标记（蓝色菱形买入，橙色方形卖出）")
        print("   - MACD指标")
        print("   - 净值曲线")
        
    except Exception as e:
        print(f"   图表生成失败: {e}")
    
    # 7. 策略评估
    print("\n7. 策略评估:")
    buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
    strategy_return = float(performance['total_return'])
    
    print(f"   买入持有收益率: {buy_hold_return:.2%}")
    print(f"   ZigZag策略收益率: {strategy_return:.2%}")
    print(f"   超额收益: {strategy_return - buy_hold_return:.2%}")
    
    if strategy_return > buy_hold_return:
        print("   ✓ 策略跑赢买入持有")
    else:
        print("   ✗ 策略跑输买入持有")
    
    # 8. 交易分析
    if trades:
        print(f"\n8. 交易分析:")
        print(f"   总交易次数: {len(trades)}")
        
        # 计算盈亏（包括止损止盈交易）
        profits = []
        trade_details = []

        for i in range(0, len(trades), 2):
            if i + 1 < len(trades):
                buy_trade = trades[i]
                sell_trade = trades[i + 1]
                if buy_trade['type'] == 'BUY' and sell_trade['type'] in ['SELL', 'STOP_LOSS', 'TAKE_PROFIT']:
                    profit = float(sell_trade['proceeds']) - float(buy_trade['cost'])
                    profit_pct = profit / float(buy_trade['cost'])
                    profits.append(profit)

                    # 交易类型标识
                    trade_type = ""
                    if sell_trade['type'] == 'STOP_LOSS':
                        trade_type = " [止损]"
                    elif sell_trade['type'] == 'TAKE_PROFIT':
                        trade_type = " [止盈]"

                    print(f"   交易{len(profits)}: ¥{profit:,.2f} ({profit_pct:.2%}){trade_type}")
                    trade_details.append({
                        'profit': profit,
                        'profit_pct': profit_pct,
                        'type': sell_trade['type']
                    })
        
        if profits:
            winning_trades = [p for p in profits if p > 0]
            losing_trades = [p for p in profits if p <= 0]

            # 按交易类型统计
            stop_loss_trades = [d for d in trade_details if d['type'] == 'STOP_LOSS']
            take_profit_trades = [d for d in trade_details if d['type'] == 'TAKE_PROFIT']
            normal_trades = [d for d in trade_details if d['type'] == 'SELL']

            print(f"\n   交易统计:")
            print(f"   盈利交易: {len(winning_trades)}/{len(profits)} ({len(winning_trades)/len(profits):.1%})")
            print(f"   正常卖出: {len(normal_trades)}次")
            print(f"   止损卖出: {len(stop_loss_trades)}次")
            print(f"   止盈卖出: {len(take_profit_trades)}次")

            if winning_trades:
                print(f"   平均盈利: ¥{sum(winning_trades)/len(winning_trades):,.2f}")
            if losing_trades:
                print(f"   平均亏损: ¥{sum(losing_trades)/len(losing_trades):,.2f}")

            # 止盈止损效果分析
            if stop_loss_trades:
                stop_loss_profits = [d['profit'] for d in stop_loss_trades]
                print(f"   止损平均亏损: ¥{sum(stop_loss_profits)/len(stop_loss_profits):,.2f}")

            if take_profit_trades:
                take_profit_profits = [d['profit'] for d in take_profit_trades]
                print(f"   止盈平均盈利: ¥{sum(take_profit_profits)/len(take_profit_profits):,.2f}")
    
    print(f"\n总结:")
    print(f"- 使用 {best_strategy['name']} 在股票 {stock_code} 上进行回测")
    print(f"- ZigZag识别了 {len(zigzag_points)} 个关键转折点")
    print(f"- 生成了 {len(signals)} 个Fibonacci回撤交易信号")
    print(f"- 执行了 {len(trades)} 次交易，最终收益率 {strategy_return:.2%}")
    print(f"- 可视化图表已保存，可查看详细的交易过程和ZigZag分析")

def create_fallback_data():
    """创建备用数据（当无法获取真实数据时使用）"""
    from datetime import datetime, timedelta
    from decimal import Decimal
    from stock_backtest.data_models import StockData
    
    base_date = datetime(2024, 1, 1)
    prices = [100, 105, 110, 115, 120, 115, 110, 105, 110, 115, 120, 125, 130]
    
    stock_data = []
    for i, close_price in enumerate(prices):
        date = base_date + timedelta(days=i)
        stock_data.append(StockData(
            symbol="DEMO",
            date=date,
            open=Decimal(str(close_price - 0.5)),
            high=Decimal(str(close_price + 1)),
            low=Decimal(str(close_price - 1)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    return stock_data

if __name__ == "__main__":
    main()
