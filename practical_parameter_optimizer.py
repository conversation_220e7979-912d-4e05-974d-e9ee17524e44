#!/usr/bin/env python3
"""
实用参数优化器 - 专门解决信号执行率低的问题
"""

import sys
import os
import random
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from improved_backtest_engine import ImprovedBacktestEngine


def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个更复杂的价格序列
    prices = [
        # 初始上涨
        100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
        # 回调
        128, 125, 122, 120, 118, 115, 112, 110, 108,
        # 强势反弹
        112, 118, 125, 132, 140, 148, 155, 162, 170, 175,
        # 震荡整理
        172, 168, 174, 171, 176, 173, 169, 172, 175, 178,
        # 下跌趋势
        175, 170, 165, 158, 152, 145, 138, 132, 125, 120,
        # 底部反弹
        122, 128, 135, 142, 148, 155, 162, 168, 175, 180,
        # 最终回调
        178, 175, 172, 168, 165, 162, 158, 155, 152, 150
    ]
    
    volumes = [1000000 + random.randint(-200000, 300000) for _ in prices]
    
    stock_data = []
    for i, (close_price, volume) in enumerate(zip(prices, volumes)):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        open_price = close_price + random.uniform(-1, 1)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)
        
        stock_data.append(StockData(
            symbol="SAMPLE",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(close_price)),
            volume=volume
        ))
    
    return stock_data


def optimize_for_execution():
    """专门优化信号执行率"""
    print("🎯 专门优化信号执行率")
    print("=" * 50)
    
    stock_data = create_sample_data()
    print(f"📊 数据点: {len(stock_data)}")
    
    # 定义更宽松的参数空间，专注于提高执行率
    param_combinations = [
        # 组合1: 最宽松条件
        {
            'name': '最宽松条件',
            'zigzag_threshold': 0.05,
            'fib_levels': [0.236, 0.382, 0.618],
            'dynamic_threshold': False,
            'volume_confirmation': False,  # 关闭成交量确认
            'rsi_filter': False,           # 关闭RSI过滤
            'min_bandwidth': 0.001,        # 极低布林带要求
            'min_signal_interval': 1,      # 最短信号间隔
            'volume_multiplier': 1.0,      # 最低成交量要求
            'rsi_overbought': 90,          # 极宽松RSI
            'rsi_oversold': 10,
            'stop_loss_pct': 0.15,         # 宽松止损
            'take_profit_pct': 0.30,       # 高止盈目标
            'commission_rate': 0.001
        },
        
        # 组合2: 适中条件
        {
            'name': '适中条件',
            'zigzag_threshold': 0.05,
            'fib_levels': [0.236, 0.382, 0.5, 0.618],
            'dynamic_threshold': False,
            'volume_confirmation': False,
            'rsi_filter': True,
            'min_bandwidth': 0.005,
            'min_signal_interval': 1,
            'volume_multiplier': 1.1,
            'rsi_overbought': 80,
            'rsi_oversold': 20,
            'stop_loss_pct': 0.10,
            'take_profit_pct': 0.25,
            'commission_rate': 0.001
        },
        
        # 组合3: 只用ZigZag，不用其他过滤
        {
            'name': '纯ZigZag策略',
            'zigzag_threshold': 0.03,
            'fib_levels': [0.382, 0.618],
            'dynamic_threshold': False,
            'volume_confirmation': False,
            'rsi_filter': False,
            'min_bandwidth': 0.001,
            'min_signal_interval': 1,
            'volume_multiplier': 1.0,
            'rsi_overbought': 100,
            'rsi_oversold': 0,
            'stop_loss_pct': 0.08,
            'take_profit_pct': 0.20,
            'commission_rate': 0.001
        },
        
        # 组合4: 高频交易版本
        {
            'name': '高频交易版本',
            'zigzag_threshold': 0.02,      # 更敏感的ZigZag
            'fib_levels': [0.236, 0.382, 0.5, 0.618, 0.786],
            'dynamic_threshold': False,
            'volume_confirmation': False,
            'rsi_filter': False,
            'min_bandwidth': 0.001,
            'min_signal_interval': 1,
            'volume_multiplier': 1.0,
            'rsi_overbought': 100,
            'rsi_oversold': 0,
            'stop_loss_pct': 0.05,         # 紧止损
            'take_profit_pct': 0.15,       # 低止盈
            'commission_rate': 0.001
        },
        
        # 组合5: 保守版本
        {
            'name': '保守版本',
            'zigzag_threshold': 0.08,
            'fib_levels': [0.382, 0.618],
            'dynamic_threshold': False,
            'volume_confirmation': False,
            'rsi_filter': True,
            'min_bandwidth': 0.01,
            'min_signal_interval': 2,
            'volume_multiplier': 1.2,
            'rsi_overbought': 75,
            'rsi_oversold': 25,
            'stop_loss_pct': 0.12,
            'take_profit_pct': 0.25,
            'commission_rate': 0.002
        }
    ]
    
    print(f"\n🧪 测试 {len(param_combinations)} 种参数组合:")
    
    results = []
    for i, params in enumerate(param_combinations):
        print(f"\n测试组合 {i+1}: {params['name']}")
        
        try:
            # 创建策略
            strategy = ImprovedZigZagFibonacciSignalGenerator(
                zigzag_threshold=params['zigzag_threshold'],
                fib_levels=params['fib_levels'],
                dynamic_threshold=params['dynamic_threshold'],
                volume_confirmation=params['volume_confirmation'],
                rsi_filter=params['rsi_filter'],
                macd_filter=False,
                min_bandwidth=params['min_bandwidth'],
                min_signal_interval=params['min_signal_interval'],
                volume_multiplier=params['volume_multiplier'],
                rsi_overbought=params['rsi_overbought'],
                rsi_oversold=params['rsi_oversold']
            )
            
            # 创建回测引擎
            engine = ImprovedBacktestEngine(
                initial_capital=100000,
                commission_rate=params['commission_rate'],
                signal_generator=strategy,
                stop_loss_pct=params['stop_loss_pct'],
                take_profit_pct=params['take_profit_pct']
            )
            
            # 运行回测
            backtest_results = engine.run_backtest({'SAMPLE': stock_data})
            performance = backtest_results['SAMPLE']['performance']
            signal_stats = performance['signal_stats']
            
            # 计算指标
            total_return = float(performance['total_return'])
            trades_count = len(performance['trades'])
            execution_rate = signal_stats['executed_signals'] / max(1, signal_stats['total_signals'])
            
            # 计算买入持有收益率
            buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
            excess_return = total_return - buy_hold_return
            
            result = {
                'name': params['name'],
                'params': params,
                'total_return': total_return,
                'excess_return': excess_return,
                'trades_count': trades_count,
                'execution_rate': execution_rate,
                'signals_total': signal_stats['total_signals'],
                'signals_executed': signal_stats['executed_signals'],
                'final_value': float(performance['final_value'])
            }
            
            results.append(result)
            
            print(f"  ✅ 总收益率: {total_return:.2%}")
            print(f"  📊 信号执行率: {execution_rate:.1%} ({signal_stats['executed_signals']}/{signal_stats['total_signals']})")
            print(f"  🔄 交易次数: {trades_count}")
            print(f"  💰 最终价值: ¥{result['final_value']:,.2f}")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    # 按执行率排序
    results.sort(key=lambda x: (x['execution_rate'], x['total_return']), reverse=True)
    
    print(f"\n🏆 结果排名 (按执行率和收益率排序):")
    print("=" * 80)
    
    for i, result in enumerate(results):
        print(f"\n第 {i+1} 名: {result['name']}")
        print(f"  📊 信号执行率: {result['execution_rate']:.1%}")
        print(f"  💰 总收益率: {result['total_return']:.2%}")
        print(f"  📈 超额收益: {result['excess_return']:.2%}")
        print(f"  🔄 交易次数: {result['trades_count']}")
        print(f"  💵 最终价值: ¥{result['final_value']:,.2f}")
        
        # 显示关键参数
        params = result['params']
        print(f"  🔧 关键设置:")
        print(f"     ZigZag阈值: {params['zigzag_threshold']:.1%}")
        print(f"     成交量确认: {params['volume_confirmation']}")
        print(f"     RSI过滤: {params['rsi_filter']}")
        print(f"     止损/止盈: {params['stop_loss_pct']:.1%}/{params['take_profit_pct']:.1%}")
    
    # 给出最终建议
    if results:
        best = results[0]
        print(f"\n💡 最终建议:")
        print("=" * 30)
        print(f"推荐使用: {best['name']}")
        print(f"该组合能够实现 {best['execution_rate']:.1%} 的信号执行率")
        print(f"总收益率: {best['total_return']:.2%}")
        
        print(f"\n🔧 推荐参数配置:")
        best_params = best['params']
        print(f"""
strategy = ImprovedZigZagFibonacciSignalGenerator(
    zigzag_threshold={best_params['zigzag_threshold']},
    fib_levels={best_params['fib_levels']},
    dynamic_threshold={best_params['dynamic_threshold']},
    volume_confirmation={best_params['volume_confirmation']},
    rsi_filter={best_params['rsi_filter']},
    min_bandwidth={best_params['min_bandwidth']},
    min_signal_interval={best_params['min_signal_interval']},
    volume_multiplier={best_params['volume_multiplier']},
    rsi_overbought={best_params['rsi_overbought']},
    rsi_oversold={best_params['rsi_oversold']}
)

engine = ImprovedBacktestEngine(
    initial_capital=100000,
    commission_rate={best_params['commission_rate']},
    signal_generator=strategy,
    stop_loss_pct={best_params['stop_loss_pct']},
    take_profit_pct={best_params['take_profit_pct']}
)
""")
        
        print(f"\n📋 关键要点:")
        print(f"1. 关闭过于严格的过滤条件（成交量确认、RSI过滤等）")
        print(f"2. 使用适中的ZigZag阈值 ({best_params['zigzag_threshold']:.1%})")
        print(f"3. 设置合理的止损止盈比例")
        print(f"4. 优先考虑信号执行率，再优化收益率")


if __name__ == "__main__":
    optimize_for_execution()
