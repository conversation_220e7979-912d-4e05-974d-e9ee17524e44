#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZigZag算法验证和ECharts可视化
包含动态阈值的ZigZag算法，生成峰谷值列表，并实现ECharts可视化
"""

import json
import os
from decimal import Decimal
from typing import List, Tuple, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import yfinance as yf
import pandas as pd


@dataclass
class StockData:
    """股票数据结构"""
    date: str
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int


class ZigZagAnalyzer:
    """ZigZag分析器，包含动态阈值功能"""
    
    def __init__(self, base_threshold: float = 0.05, dynamic_threshold: bool = True, atr_period: int = 14):
        """
        初始化ZigZag分析器
        
        Args:
            base_threshold: 基础阈值（百分比）
            dynamic_threshold: 是否使用动态阈值
            atr_period: ATR计算周期
        """
        self.base_threshold = base_threshold
        self.dynamic_threshold = dynamic_threshold
        self.atr_period = atr_period
    
    def calculate_atr(self, stock_data: List[StockData]) -> List[Decimal]:
        """计算平均真实波幅(ATR)"""
        if len(stock_data) < 2:
            return []
        
        true_ranges = []
        for i in range(1, len(stock_data)):
            current = stock_data[i]
            previous = stock_data[i-1]
            
            # 计算真实波幅
            tr1 = current.high - current.low
            tr2 = abs(current.high - previous.close)
            tr3 = abs(current.low - previous.close)
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        # 计算ATR
        atr_values = []
        for i in range(len(true_ranges)):
            if i < self.atr_period - 1:
                atr_values.append(Decimal(0))
            else:
                start_idx = max(0, i - self.atr_period + 1)
                atr = sum(true_ranges[start_idx:i+1]) / min(self.atr_period, i + 1)
                atr_values.append(atr)
        
        return atr_values
    
    def calculate_zigzag_points(self, stock_data: List[StockData]) -> List[Tuple[int, Decimal, str, str]]:
        """
        计算ZigZag转折点
        
        Returns:
            List of (index, price, type, date) where type is 'HIGH' or 'LOW'
        """
        if len(stock_data) < 3:
            return []
        
        # 计算ATR用于动态阈值
        atr_values = []
        if self.dynamic_threshold:
            atr_values = self.calculate_atr(stock_data)
        
        zigzag_points = []
        current_trend = None
        last_extreme_idx = 0
        last_extreme_high = stock_data[0].high
        last_extreme_low = stock_data[0].low
        
        for i in range(1, len(stock_data)):
            current_high = stock_data[i].high
            current_low = stock_data[i].low
            
            # 计算当前阈值
            if self.dynamic_threshold and len(atr_values) > i and atr_values[i] > 0:
                # 使用动态阈值
                base_price = stock_data[max(0, i-10)].close  # 使用10天前的价格作为基准
                current_threshold = float(atr_values[i] / base_price)
                current_threshold = max(current_threshold, self.base_threshold * 0.5)  # 最小阈值
                current_threshold = min(current_threshold, self.base_threshold * 2.0)  # 最大阈值
            else:
                current_threshold = self.base_threshold
            
            # 初始化趋势
            if current_trend is None:
                if current_high > last_extreme_high * (1 + current_threshold):
                    current_trend = 'UP'
                elif current_low < last_extreme_low * (1 - current_threshold):
                    current_trend = 'DOWN'
                
                last_extreme_idx = i
                last_extreme_high = current_high
                last_extreme_low = current_low
                continue
            
            if current_trend == 'UP':
                # 上涨趋势中，寻找更高的高点或转折点
                if current_high > last_extreme_high:
                    # 创新高，更新高点
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 检查是否出现显著回调
                    price_decline = (last_extreme_high - current_low) / last_extreme_high
                    if price_decline >= current_threshold:
                        # 趋势转为下跌，记录前一个高点
                        zigzag_points.append((
                            last_extreme_idx, 
                            last_extreme_high, 
                            'HIGH',
                            stock_data[last_extreme_idx].date
                        ))
                        current_trend = 'DOWN'
                        last_extreme_idx = i
                        last_extreme_high = current_high
                        last_extreme_low = current_low
            
            elif current_trend == 'DOWN':
                # 下跌趋势中，寻找更低的低点或转折点
                if current_low < last_extreme_low:
                    # 创新低，更新低点
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 检查是否出现显著反弹
                    price_rise = (current_high - last_extreme_low) / last_extreme_low
                    if price_rise >= current_threshold:
                        # 趋势转为上涨，记录前一个低点
                        zigzag_points.append((
                            last_extreme_idx, 
                            last_extreme_low, 
                            'LOW',
                            stock_data[last_extreme_idx].date
                        ))
                        current_trend = 'UP'
                        last_extreme_idx = i
                        last_extreme_high = current_high
                        last_extreme_low = current_low
        
        # 添加最后一个极值点
        if current_trend is not None:
            if current_trend == 'UP':
                zigzag_points.append((
                    last_extreme_idx, 
                    last_extreme_high, 
                    'HIGH',
                    stock_data[last_extreme_idx].date
                ))
            else:
                zigzag_points.append((
                    last_extreme_idx, 
                    last_extreme_low, 
                    'LOW',
                    stock_data[last_extreme_idx].date
                ))
        
        return zigzag_points


class StockDataLoader:
    """股票数据加载器"""
    
    @staticmethod
    def load_stock_data(symbol: str, period: str = "1y") -> List[StockData]:
        """
        从Yahoo Finance加载股票数据
        
        Args:
            symbol: 股票代码
            period: 时间周期 (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
        
        Returns:
            股票数据列表
        """
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            
            if hist.empty:
                print(f"无法获取股票 {symbol} 的数据")
                return []
            
            stock_data = []
            for date, row in hist.iterrows():
                stock_data.append(StockData(
                    date=date.strftime('%Y-%m-%d'),
                    open=Decimal(str(row['Open'])),
                    high=Decimal(str(row['High'])),
                    low=Decimal(str(row['Low'])),
                    close=Decimal(str(row['Close'])),
                    volume=int(row['Volume'])
                ))
            
            return stock_data
            
        except Exception as e:
            print(f"加载股票数据时出错: {e}")
            return []


class EChartsVisualizer:
    """ECharts可视化器"""
    
    def __init__(self):
        self.template_path = "zigzag_chart_template.html"
    
    def generate_chart_data(self, stock_data: List[StockData], zigzag_points: List[Tuple[int, Decimal, str, str]]) -> Dict[str, Any]:
        """生成图表数据"""
        # K线数据
        kline_data = []
        for data in stock_data:
            kline_data.append([
                data.date,
                float(data.open),
                float(data.close),
                float(data.low),
                float(data.high),
                data.volume
            ])
        
        # ZigZag点数据
        zigzag_data = []
        peak_points = []  # 峰点
        valley_points = []  # 谷点
        
        for idx, price, point_type, date in zigzag_points:
            point_data = {
                'name': f'{point_type}_{idx}',
                'coord': [date, float(price)],
                'value': float(price),
                'type': point_type,
                'index': idx
            }
            zigzag_data.append(point_data)
            
            if point_type == 'HIGH':
                peak_points.append(point_data)
            else:
                valley_points.append(point_data)
        
        return {
            'kline_data': kline_data,
            'zigzag_data': zigzag_data,
            'peak_points': peak_points,
            'valley_points': valley_points
        }
    
    def create_html_chart(self, chart_data: Dict[str, Any], symbol: str, output_file: str = "zigzag_chart.html"):
        """创建HTML图表文件"""
        html_content = self._generate_html_template(chart_data, symbol)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"图表已生成: {output_file}")
        return output_file
