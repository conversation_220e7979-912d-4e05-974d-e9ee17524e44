#!/usr/bin/env python3
"""
改进的信号生成器测试
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
import unittest

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData, TradingSignal
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator


class TestImprovedSignalGenerator(unittest.TestCase):
    """改进的信号生成器测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建示例数据
        self.sample_data = self._create_sample_data()
        
        # 创建基础策略实例
        self.basic_strategy = ImprovedZigZagFibonacciSignalGenerator(
            zigzag_threshold=0.05,
            fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
        )
        
        # 创建改进策略实例
        self.improved_strategy = ImprovedZigZagFibonacciSignalGenerator(
            zigzag_threshold=0.05,
            fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
            dynamic_threshold=True,
            volume_confirmation=True,
            rsi_filter=True,
            macd_filter=True,
            min_bandwidth=0.02,
            min_signal_interval=3
        )

    def _create_sample_data(self):
        """创建示例股票数据"""
        base_date = datetime(2024, 1, 1)
        
        # 创建一个有明显趋势和回调的价格序列
        prices = [
            # 上涨趋势
            100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
            # 回调
            128, 125, 122, 120, 118,
            # 再次上涨
            120, 123, 126, 130, 134, 138, 142, 145, 148, 150,
            # 回调
            148, 145, 142, 140, 138,
            # 下跌趋势
            138, 135, 132, 128, 125, 122, 118, 115, 112, 110,
            # 反弹
            112, 115, 118, 122, 125, 128, 130
        ]
        
        volumes = [
            # 成交量随着价格波动变化
            1000000, 1100000, 1200000, 1300000, 1400000, 1350000, 1300000, 1400000, 1500000, 1600000, 1700000,
            1500000, 1400000, 1300000, 1200000, 1100000,
            1150000, 1200000, 1250000, 1300000, 1350000, 1400000, 1450000, 1500000, 1550000, 1600000,
            1400000, 1300000, 1200000, 1100000, 1000000,
            1050000, 1100000, 1150000, 1200000, 1250000, 1300000, 1350000, 1400000, 1450000, 1500000,
            1300000, 1200000, 1100000, 1000000, 900000,
            950000, 1000000, 1050000, 1100000, 1150000, 1200000, 1250000
        ]
        
        stock_data = []
        for i, (close_price, volume) in enumerate(zip(prices, volumes)):
            date = base_date + timedelta(days=i)
            
            # 生成OHLC数据
            open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
            high_price = max(open_price, close_price) + 0.5
            low_price = min(open_price, close_price) - 0.5
            
            # 添加技术指标数据
            if i >= 12:  # 有足够的数据计算指标
                # 简化的MACD和布林带数据
                if close_price > 120:  # 价格上涨时
                    macd = Decimal('0.5')
                    macd_signal = Decimal('0.3')
                    bb_upper = Decimal(str(close_price * 1.02))
                    bb_middle = Decimal(str(close_price))
                    bb_lower = Decimal(str(close_price * 0.98))
                else:  # 价格下跌时
                    macd = Decimal('-0.5')
                    macd_signal = Decimal('-0.3')
                    bb_upper = Decimal(str(close_price * 1.02))
                    bb_middle = Decimal(str(close_price))
                    bb_lower = Decimal(str(close_price * 0.98))
            else:
                macd = None
                macd_signal = None
                bb_upper = None
                bb_middle = None
                bb_lower = None
            
            stock_data.append(StockData(
                symbol="TEST",
                date=date,
                open=Decimal(str(open_price)),
                high=Decimal(str(high_price)),
                low=Decimal(str(low_price)),
                close=Decimal(str(close_price)),
                volume=volume,
                macd=macd,
                macd_signal=macd_signal,
                bb_upper=bb_upper,
                bb_middle=bb_middle,
                bb_lower=bb_lower
            ))
        
        return stock_data

    def test_basic_signal_generation(self):
        """测试基础信号生成"""
        signals = self.basic_strategy.generate_signals(self.sample_data)
        self.assertIsInstance(signals, list)
        # 验证信号类型
        for signal in signals:
            self.assertIsInstance(signal, TradingSignal)
            self.assertIn(signal.signal_type, ['BUY', 'SELL'])

    def test_improved_signal_generation(self):
        """测试改进信号生成"""
        signals = self.improved_strategy.generate_signals(self.sample_data)
        self.assertIsInstance(signals, list)
        # 验证信号类型
        for signal in signals:
            self.assertIsInstance(signal, TradingSignal)
            self.assertIn(signal.signal_type, ['BUY', 'SELL'])

    def test_dynamic_threshold_calculation(self):
        """测试动态阈值计算"""
        atr_values = self.improved_strategy._calculate_atr(self.sample_data, 14)
        self.assertIsInstance(atr_values, list)
        self.assertGreater(len(atr_values), 0)
        # 验证ATR值都是非负数
        for atr in atr_values:
            self.assertGreaterEqual(atr, 0)

    def test_zigzag_dynamic_calculation(self):
        """测试动态ZigZag计算"""
        atr_values = self.improved_strategy._calculate_atr(self.sample_data, 14)
        zigzag_points = self.improved_strategy._calculate_zigzag_dynamic(self.sample_data, atr_values)
        self.assertIsInstance(zigzag_points, list)
        # 验证ZigZag点的格式
        for point in zigzag_points:
            self.assertIsInstance(point, tuple)
            self.assertEqual(len(point), 3)
            self.assertIsInstance(point[0], int)  # 索引
            self.assertIsInstance(point[1], Decimal)  # 价格
            self.assertIn(point[2], ['HIGH', 'LOW'])  # 类型

    def test_trend_strength_check(self):
        """测试趋势强度检查"""
        # 创建示例ZigZag点
        zigzag_points = [
            (0, Decimal('100'), 'LOW'),
            (10, Decimal('130'), 'HIGH'),
            (15, Decimal('118'), 'LOW')
        ]
        result = self.improved_strategy._check_trend_strength(zigzag_points)
        self.assertIsInstance(result, bool)

    def test_volatility_check(self):
        """测试波动性检查"""
        atr_values = self.improved_strategy._calculate_atr(self.sample_data, 14)
        result = self.improved_strategy._check_volatility(atr_values, 10)
        self.assertIsInstance(result, bool)

    def test_volume_confirmation(self):
        """测试成交量确认"""
        result = self.improved_strategy._check_volume_confirmation(self.sample_data, 10)
        self.assertIsInstance(result, bool)

    def test_rsi_filter(self):
        """测试RSI过滤"""
        result = self.improved_strategy._check_rsi_filter(self.sample_data, 20)
        self.assertIsInstance(result, bool)

    def test_macd_filter(self):
        """测试MACD过滤"""
        result = self.improved_strategy._check_macd_filter(self.sample_data, 20)
        self.assertIsInstance(result, bool)

    def test_bollinger_bandwidth_check(self):
        """测试布林带宽度检查"""
        result = self.improved_strategy._check_bollinger_bandwidth(self.sample_data, 20)
        self.assertIsInstance(result, bool)

    def test_signal_interval_check(self):
        """测试信号间隔检查"""
        from datetime import datetime, timedelta
        current_date = datetime(2024, 1, 10)
        self.improved_strategy.last_signal_date = datetime(2024, 1, 5)
        result = self.improved_strategy._is_signal_interval_valid(current_date)
        self.assertIsInstance(result, bool)

    def test_empty_data_handling(self):
        """测试空数据处理"""
        empty_data = []
        signals = self.basic_strategy.generate_signals(empty_data)
        self.assertEqual(signals, [])

    def test_insufficient_data_handling(self):
        """测试数据不足处理"""
        insufficient_data = self.sample_data[:5]  # 只取前5个数据点
        signals = self.basic_strategy.generate_signals(insufficient_data)
        self.assertEqual(signals, [])


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)