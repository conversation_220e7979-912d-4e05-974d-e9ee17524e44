#!/usr/bin/env python3
"""
调试回测执行问题
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine


def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个有明显趋势和回调的价格序列
    prices = [
        # 上涨趋势
        100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130,
        # 回调
        128, 125, 122, 120, 118,
        # 再次上涨
        120, 123, 126, 130, 134, 138, 142, 145, 148, 150,
        # 回调
        148, 145, 142, 140, 138,
        # 下跌趋势
        138, 135, 132, 128, 125, 122, 118, 115, 112, 110,
        # 反弹
        112, 115, 118, 122, 125, 128, 130
    ]
    
    volumes = [1000000] * len(prices)  # 简化成交量
    
    stock_data = []
    for i, (close_price, volume) in enumerate(zip(prices, volumes)):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
        high_price = max(open_price, close_price) + 0.5
        low_price = min(open_price, close_price) - 0.5
        
        stock_data.append(StockData(
            symbol="SAMPLE",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=volume
        ))
    
    return stock_data


class DebugBacktestEngine(BacktestEngine):
    """调试版本的回测引擎"""
    
    def _execute_backtest(self, data, signals):
        """执行回测逻辑，包含调试信息"""
        capital = self.initial_capital
        position = 0  # 持仓数量
        trades = []
        buy_price = None  # 记录买入价格，用于止损止盈计算

        signal_dict = {signal.date: signal for signal in signals}
        
        print(f"\n=== 回测执行调试 ===")
        print(f"初始资金: ¥{float(capital):,.2f}")
        print(f"总信号数: {len(signals)}")
        print(f"信号详情:")
        for i, signal in enumerate(signals):
            print(f"  {i+1}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type} ¥{float(signal.price):.2f}")
        
        print(f"\n=== 逐日执行过程 ===")
        
        for i, stock_data in enumerate(data):
            # 检查止损和止盈条件（如果有持仓）
            if position > 0 and buy_price is not None:
                # 计算当前价格相对于买入价格的变化
                price_decline = (buy_price - stock_data.low) / buy_price
                price_gain = (stock_data.high - buy_price) / buy_price

                # 检查止盈条件（优先级高于止损）
                if price_gain >= self.take_profit_pct:
                    # 触发止盈，按当日最高价卖出
                    take_profit_price = stock_data.high
                    proceeds = position * take_profit_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'TAKE_PROFIT',
                        'shares': position,
                        'price': take_profit_price,
                        'proceeds': proceeds,
                        'reason': f'止盈卖出 (涨幅: {price_gain:.2%})'
                    })
                    print(f"  {stock_data.date.strftime('%Y-%m-%d')}: 止盈卖出 {position}股 @ ¥{float(take_profit_price):.2f}")
                    position = 0
                    buy_price = None
                    continue  # 止盈后不再处理当日的其他信号

                # 检查止损条件
                elif price_decline >= self.stop_loss_pct:
                    # 触发止损，按当日最低价卖出
                    stop_loss_price = stock_data.low
                    proceeds = position * stop_loss_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'STOP_LOSS',
                        'shares': position,
                        'price': stop_loss_price,
                        'proceeds': proceeds,
                        'reason': f'止损卖出 (跌幅: {price_decline:.2%})'
                    })
                    print(f"  {stock_data.date.strftime('%Y-%m-%d')}: 止损卖出 {position}股 @ ¥{float(stop_loss_price):.2f}")
                    position = 0
                    buy_price = None
                    continue  # 止损后不再处理当日的其他信号

            # 处理交易信号
            if stock_data.date in signal_dict:
                signal = signal_dict[stock_data.date]
                print(f"  {stock_data.date.strftime('%Y-%m-%d')}: 收到{signal.signal_type}信号 @ ¥{float(signal.price):.2f}")
                print(f"    当前资金: ¥{float(capital):,.2f}, 持仓: {position}股")

                if signal.signal_type == 'BUY' and capital > 0 and position == 0:
                    # 买入 - 只有在没有持仓时才买入
                    max_shares = int(capital / (signal.price * (Decimal(1) + self.commission_rate)))
                    if max_shares > 0:
                        cost = max_shares * signal.price * (Decimal(1) + self.commission_rate)
                        capital -= cost
                        position += max_shares
                        buy_price = signal.price  # 记录买入价格
                        trades.append({
                            'date': signal.date,
                            'type': 'BUY',
                            'shares': max_shares,
                            'price': signal.price,
                            'cost': cost,
                            'reason': signal.reason
                        })
                        print(f"    ✓ 执行买入: {max_shares}股 @ ¥{float(signal.price):.2f}, 成本: ¥{float(cost):,.2f}")
                    else:
                        print(f"    ✗ 买入失败: 资金不足")

                elif signal.signal_type == 'SELL' and position > 0:
                    # 卖出
                    proceeds = position * signal.price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': signal.date,
                        'type': 'SELL',
                        'shares': position,
                        'price': signal.price,
                        'proceeds': proceeds,
                        'reason': signal.reason
                    })
                    print(f"    ✓ 执行卖出: {position}股 @ ¥{float(signal.price):.2f}, 收入: ¥{float(proceeds):,.2f}")
                    position = 0
                    buy_price = None  # 清除买入价格记录
                
                else:
                    if signal.signal_type == 'BUY':
                        if capital <= 0:
                            print(f"    ✗ 买入失败: 资金不足")
                        elif position > 0:
                            print(f"    ✗ 买入失败: 已有持仓")
                    elif signal.signal_type == 'SELL':
                        if position <= 0:
                            print(f"    ✗ 卖出失败: 无持仓")

        # 计算最终价值
        final_price = data[-1].close if data else Decimal(0)
        final_value = capital + position * final_price

        # 统计止损和止盈次数
        stop_loss_trades = [t for t in trades if t['type'] == 'STOP_LOSS']
        take_profit_trades = [t for t in trades if t['type'] == 'TAKE_PROFIT']
        
        print(f"\n=== 回测结果 ===")
        print(f"最终资金: ¥{float(capital):,.2f}")
        print(f"最终持仓: {position}股")
        print(f"最终价值: ¥{float(final_value):,.2f}")
        print(f"总交易次数: {len(trades)}")

        return {
            'initial_capital': self.initial_capital,
            'final_value': final_value,
            'total_return': (final_value - self.initial_capital) / self.initial_capital,
            'trades': trades,
            'final_position': position,
            'stop_loss_count': len(stop_loss_trades),
            'stop_loss_pct': float(self.stop_loss_pct),
            'take_profit_count': len(take_profit_trades),
            'take_profit_pct': float(self.take_profit_pct)
        }


def main():
    """主函数"""
    print("=== 调试回测执行问题 ===\n")
    
    # 创建示例数据
    stock_data = create_sample_data()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 创建宽松的策略以获得信号
    strategy = ImprovedZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
        dynamic_threshold=False,
        volume_confirmation=False,
        rsi_filter=False,
        macd_filter=False,
        min_bandwidth=0.005,
        min_signal_interval=1,
        atr_period=14,
        volume_multiplier=1.1,
        rsi_overbought=80,
        rsi_oversold=20
    )
    
    # 创建调试版本的回测引擎
    debug_engine = DebugBacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=strategy,
        stop_loss_pct=0.05,
        take_profit_pct=0.15
    )

    # 先处理数据（添加技术指标）
    processed_data = debug_engine._calculate_indicators(stock_data)
    print(f"数据处理完成，添加了技术指标")

    # 使用处理后的数据生成信号
    signals = strategy.generate_signals(processed_data)
    print(f"使用处理后的数据生成了 {len(signals)} 个信号")

    # 直接执行回测（不使用run_backtest方法）
    backtest_result = debug_engine._execute_backtest(processed_data, signals)


if __name__ == "__main__":
    main()
