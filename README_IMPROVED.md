# 改进的ZigZag Fibonacci 交易策略

## 1. 项目概述

本项目是ZigZag Fibonacci交易策略的改进版本，通过增加多重信号过滤机制、动态参数调整和增强的风险管理功能，显著提高了策略的信号质量、风险控制能力和整体表现。

## 2. 策略原理

### 2.1 核心思想
结合ZigZag指标的趋势识别能力和Fibonacci回撤水平的精确入场点选择，形成一套系统化的交易方法。

### 2.2 改进点
1. **动态参数调整**: 根据市场波动性动态调整ZigZag阈值
2. **多重信号过滤**: 通过成交量、RSI、MACD等指标过滤信号
3. **增强风险管理**: 动态止损止盈、仓位管理和最大回撤控制
4. **参数优化**: 支持网格搜索和遗传算法优化参数

## 3. 主要特性

### 3.1 信号生成优化
- 趋势强度判断
- 价格波动性过滤
- 时间间隔过滤

### 3.2 动态参数调整
- 基于ATR的动态ZigZag阈值
- 动态Fibonacci水平

### 3.3 成交量确认机制
- 成交量放大确认
- 量价背离检测

### 3.4 增强信号过滤条件
- RSI指标过滤
- MACD指标过滤
- 布林带宽度过滤

### 3.5 风险管理体系
- 动态止损止盈
- 仓位管理
- 最大回撤控制

## 4. 安装和使用

### 4.1 环境要求
- Python 3.7+
- 相关依赖包（见pyproject.toml）

### 4.2 安装依赖
```bash
pip install -r requirements.txt
```

### 4.3 基本使用
```python
from stock_backtest.improved_signal_generator import ImprovedZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine

# 创建改进的策略实例
strategy = ImprovedZigZagFibonacciSignalGenerator(
    zigzag_threshold=0.05,
    fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786],
    dynamic_threshold=True,
    volume_confirmation=True,
    rsi_filter=True,
    macd_filter=True
)

# 生成交易信号
signals = strategy.generate_signals(stock_data)

# 创建回测引擎
backtest_engine = BacktestEngine(
    initial_capital=100000,
    commission_rate=0.001,
    signal_generator=strategy,
    stop_loss_pct=0.05,
    take_profit_pct=0.15
)

# 运行回测
results = backtest_engine.run_backtest(stock_data_dict)
```

## 5. 文件结构

```
stock_backtest/
├── __init__.py
├── backtest_engine.py
├── data_loader.py
├── data_models.py
├── indicators.py
├── signal_generator.py
├── improved_signal_generator.py  # 改进的信号生成器
├── visualization.py
├── ...
├── improved_zigzag_fibonacci_demo.py  # 演示程序
├── test_improved_signal_generator.py  # 测试用例
├── ...
├── zigzag_fibonacci_improvement_plan.md  # 改进计划
├── improved_signal_generator_design.md  # 设计文档
├── improved_strategy_validation_report.md  # 验证报告
├── zigzag_fibonacci_improvement_summary.md  # 改进总结
└── improved_zigzag_fibonacci_architecture.md  # 架构文档
```

## 6. 运行示例

```bash
# 运行改进策略演示
python improved_zigzag_fibonacci_demo.py

# 运行测试用例
python test_improved_signal_generator.py
```

## 7. 策略参数

### 7.1 策略参数
| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| zigzag_threshold | 0.05 | ZigZag阈值 |
| fib_levels | [0.236, 0.382, 0.5, 0.618, 0.786] | Fibonacci回撤水平 |
| dynamic_threshold | False | 是否使用动态阈值 |
| volume_confirmation | True | 是否需要成交量确认 |
| rsi_filter | True | 是否使用RSI过滤 |
| macd_filter | True | 是否使用MACD过滤 |
| min_bandwidth | 0.02 | 最小布林带宽度 |
| min_signal_interval | 5 | 最小信号间隔(天) |

### 7.2 回测参数
| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| initial_capital | 100000 | 初始资金 |
| commission_rate | 0.001 | 手续费率 |
| stop_loss_pct | 0.05 | 止损百分比 |
| take_profit_pct | 0.15 | 止盈百分比 |

## 8. 测试和验证

### 8.1 单元测试
```bash
python test_improved_signal_generator.py
```

### 8.2 集成测试
通过`improved_zigzag_fibonacci_demo.py`验证整体功能。

### 8.3 真实数据测试
在真实股票数据上验证策略效果。

## 9. 性能表现

### 9.1 信号质量
- 通过多重过滤机制显著提高信号质量
- 减少假信号的产生

### 9.2 风险控制
- 动态止损止盈适应不同市场环境
- 仓位管理优化资金利用效率
- 最大回撤控制保护本金安全

### 9.3 收益表现
- 在不同市场环境下表现更加稳健
- 保持收益能力的同时降低交易风险

## 10. 未来改进方向

### 10.1 参数优化
- 实现自动参数优化功能
- 增加更多优化算法

### 10.2 多时间框架
- 结合不同时间周期的信号
- 提高信号的可靠性

### 10.3 机器学习集成
- 使用机器学习模型优化信号生成
- 增强策略的预测能力

### 10.4 实盘交易支持
- 增加实盘交易接口
- 实现实时信号生成

## 11. 文档

- [改进计划](zigzag_fibonacci_improvement_plan.md)
- [设计文档](improved_signal_generator_design.md)
- [验证报告](improved_strategy_validation_report.md)
- [改进总结](zigzag_fibonacci_improvement_summary.md)
- [架构文档](improved_zigzag_fibonacci_architecture.md)

## 12. 许可证

本项目仅供学习和研究使用，实际投资请谨慎评估风险。