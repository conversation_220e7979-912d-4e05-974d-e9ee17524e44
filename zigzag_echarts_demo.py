#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZigZag算法验证和ECharts可视化演示
包含动态阈值的ZigZag算法，生成峰谷值列表，并实现ECharts可视化
支持K线图上标注峰谷点，鼠标悬停时显示峰峰连线、谷谷连线和交叉点
"""

import json
import os
from decimal import Decimal
from typing import List, Tuple, Dict, Any
from datetime import datetime
from stock_backtest import get_stock_data, dataframe_to_stock_data, StockData


class ZigZagAnalyzer:
    """ZigZag分析器，包含动态阈值功能"""
    
    def __init__(self, base_threshold: float = 0.05, dynamic_threshold: bool = True, atr_period: int = 14):
        """
        初始化ZigZag分析器
        
        Args:
            base_threshold: 基础阈值（百分比）
            dynamic_threshold: 是否使用动态阈值
            atr_period: ATR计算周期
        """
        self.base_threshold = base_threshold
        self.dynamic_threshold = dynamic_threshold
        self.atr_period = atr_period
    
    def calculate_atr(self, stock_data: List[StockData]) -> List[Decimal]:
        """计算平均真实波幅(ATR)"""
        if len(stock_data) < 2:
            return []
        
        true_ranges = []
        for i in range(1, len(stock_data)):
            current = stock_data[i]
            previous = stock_data[i-1]
            
            # 计算真实波幅
            tr1 = current.high - current.low
            tr2 = abs(current.high - previous.close)
            tr3 = abs(current.low - previous.close)
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        # 计算ATR
        atr_values = []
        for i in range(len(true_ranges)):
            if i < self.atr_period - 1:
                atr_values.append(Decimal(0))
            else:
                start_idx = max(0, i - self.atr_period + 1)
                atr = sum(true_ranges[start_idx:i+1]) / min(self.atr_period, i + 1)
                atr_values.append(atr)
        
        return atr_values
    
    def calculate_zigzag_points(self, stock_data: List[StockData]) -> List[Tuple[int, Decimal, str, str]]:
        """
        计算ZigZag转折点
        
        Returns:
            List of (index, price, type, date) where type is 'HIGH' or 'LOW'
        """
        if len(stock_data) < 3:
            return []
        
        # 计算ATR用于动态阈值
        atr_values = []
        if self.dynamic_threshold:
            atr_values = self.calculate_atr(stock_data)
        
        zigzag_points = []
        current_trend = None
        last_extreme_idx = 0
        last_extreme_high = stock_data[0].high
        last_extreme_low = stock_data[0].low
        
        for i in range(1, len(stock_data)):
            current_high = stock_data[i].high
            current_low = stock_data[i].low
            
            # 计算当前阈值
            if self.dynamic_threshold and len(atr_values) > i and atr_values[i] > 0:
                # 使用动态阈值
                base_price = stock_data[max(0, i-10)].close  # 使用10天前的价格作为基准
                current_threshold = float(atr_values[i] / base_price)
                current_threshold = max(current_threshold, self.base_threshold * 0.5)  # 最小阈值
                current_threshold = min(current_threshold, self.base_threshold * 2.0)  # 最大阈值
            else:
                current_threshold = self.base_threshold

            # 转换为Decimal进行计算
            threshold_decimal = Decimal(str(current_threshold))

            # 初始化趋势
            if current_trend is None:
                if current_high > last_extreme_high * (Decimal('1') + threshold_decimal):
                    current_trend = 'UP'
                elif current_low < last_extreme_low * (Decimal('1') - threshold_decimal):
                    current_trend = 'DOWN'
                
                last_extreme_idx = i
                last_extreme_high = current_high
                last_extreme_low = current_low
                continue
            
            if current_trend == 'UP':
                # 上涨趋势中，寻找更高的高点或转折点
                if current_high > last_extreme_high:
                    # 创新高，更新高点
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 检查是否出现显著回调
                    price_decline = (last_extreme_high - current_low) / last_extreme_high
                    if price_decline >= threshold_decimal:
                        # 趋势转为下跌，记录前一个高点
                        zigzag_points.append((
                            last_extreme_idx, 
                            last_extreme_high, 
                            'HIGH',
                            stock_data[last_extreme_idx].date.strftime('%Y-%m-%d')
                        ))
                        current_trend = 'DOWN'
                        last_extreme_idx = i
                        last_extreme_high = current_high
                        last_extreme_low = current_low
            
            elif current_trend == 'DOWN':
                # 下跌趋势中，寻找更低的低点或转折点
                if current_low < last_extreme_low:
                    # 创新低，更新低点
                    last_extreme_idx = i
                    last_extreme_high = current_high
                    last_extreme_low = current_low
                else:
                    # 检查是否出现显著反弹
                    price_rise = (current_high - last_extreme_low) / last_extreme_low
                    if price_rise >= threshold_decimal:
                        # 趋势转为上涨，记录前一个低点
                        zigzag_points.append((
                            last_extreme_idx, 
                            last_extreme_low, 
                            'LOW',
                            stock_data[last_extreme_idx].date.strftime('%Y-%m-%d')
                        ))
                        current_trend = 'UP'
                        last_extreme_idx = i
                        last_extreme_high = current_high
                        last_extreme_low = current_low
        
        # 添加最后一个极值点
        if current_trend is not None:
            if current_trend == 'UP':
                zigzag_points.append((
                    last_extreme_idx, 
                    last_extreme_high, 
                    'HIGH',
                    stock_data[last_extreme_idx].date.strftime('%Y-%m-%d')
                ))
            else:
                zigzag_points.append((
                    last_extreme_idx, 
                    last_extreme_low, 
                    'LOW',
                    stock_data[last_extreme_idx].date.strftime('%Y-%m-%d')
                ))
        
        return zigzag_points


class TrendLineAnalyzer:
    """趋势线分析器，用于计算峰峰连线、谷谷连线和交叉点"""
    
    @staticmethod
    def find_peak_to_peak_lines(zigzag_points: List[Tuple[int, Decimal, str, str]], current_index: int, lookback: int = 5) -> List[Dict]:
        """找到最近的峰峰连线"""
        peaks = [(idx, price, date) for idx, price, point_type, date in zigzag_points if point_type == 'HIGH' and idx <= current_index]
        
        if len(peaks) < 2:
            return []
        
        # 取最近的几个峰点
        recent_peaks = peaks[-min(lookback, len(peaks)):]
        lines = []
        
        # 生成峰峰连线
        for i in range(len(recent_peaks) - 1):
            for j in range(i + 1, len(recent_peaks)):
                peak1 = recent_peaks[i]
                peak2 = recent_peaks[j]
                
                lines.append({
                    'type': 'peak_to_peak',
                    'start': {'index': peak1[0], 'price': float(peak1[1]), 'date': peak1[2]},
                    'end': {'index': peak2[0], 'price': float(peak2[1]), 'date': peak2[2]}
                })
        
        return lines
    
    @staticmethod
    def find_valley_to_valley_lines(zigzag_points: List[Tuple[int, Decimal, str, str]], current_index: int, lookback: int = 5) -> List[Dict]:
        """找到最近的谷谷连线"""
        valleys = [(idx, price, date) for idx, price, point_type, date in zigzag_points if point_type == 'LOW' and idx <= current_index]
        
        if len(valleys) < 2:
            return []
        
        # 取最近的几个谷点
        recent_valleys = valleys[-min(lookback, len(valleys)):]
        lines = []
        
        # 生成谷谷连线
        for i in range(len(recent_valleys) - 1):
            for j in range(i + 1, len(recent_valleys)):
                valley1 = recent_valleys[i]
                valley2 = recent_valleys[j]
                
                lines.append({
                    'type': 'valley_to_valley',
                    'start': {'index': valley1[0], 'price': float(valley1[1]), 'date': valley1[2]},
                    'end': {'index': valley2[0], 'price': float(valley2[1]), 'date': valley2[2]}
                })
        
        return lines
    
    @staticmethod
    def find_line_intersections(peak_lines: List[Dict], valley_lines: List[Dict]) -> List[Dict]:
        """找到峰峰连线和谷谷连线的交叉点"""
        intersections = []
        
        for peak_line in peak_lines:
            for valley_line in valley_lines:
                intersection = TrendLineAnalyzer._calculate_line_intersection(peak_line, valley_line)
                if intersection:
                    intersections.append(intersection)
        
        return intersections
    
    @staticmethod
    def _calculate_line_intersection(line1: Dict, line2: Dict) -> Dict:
        """计算两条线的交点"""
        # 简化的交点计算，实际应用中可能需要更复杂的算法
        # 这里返回一个示例交点
        x1, y1 = line1['start']['index'], line1['start']['price']
        x2, y2 = line1['end']['index'], line1['end']['price']
        x3, y3 = line2['start']['index'], line2['start']['price']
        x4, y4 = line2['end']['index'], line2['end']['price']
        
        # 计算交点
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return None  # 平行线
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        
        # 交点坐标
        ix = x1 + t * (x2 - x1)
        iy = y1 + t * (y2 - y1)
        
        return {
            'type': 'intersection',
            'index': ix,
            'price': iy,
            'lines': [line1, line2]
        }


class EChartsVisualizer:
    """ECharts可视化器"""
    
    def generate_chart_data(self, stock_data: List[StockData], zigzag_points: List[Tuple[int, Decimal, str, str]]) -> Dict[str, Any]:
        """生成图表数据"""
        # K线数据
        kline_data = []
        for data in stock_data:
            kline_data.append([
                data.date.strftime('%Y-%m-%d'),
                float(data.open),
                float(data.close),
                float(data.low),
                float(data.high),
                data.volume
            ])
        
        # ZigZag点数据
        zigzag_data = []
        peak_points = []  # 峰点
        valley_points = []  # 谷点
        
        for idx, price, point_type, date in zigzag_points:
            point_data = {
                'name': f'{point_type}_{idx}',
                'coord': [date, float(price)],
                'value': float(price),
                'type': point_type,
                'index': idx
            }
            zigzag_data.append(point_data)
            
            if point_type == 'HIGH':
                peak_points.append(point_data)
            else:
                valley_points.append(point_data)
        
        # 转换zigzag_points为JSON可序列化的格式
        zigzag_raw_serializable = []
        for idx, price, point_type, date in zigzag_points:
            zigzag_raw_serializable.append([idx, float(price), point_type, date])

        return {
            'kline_data': kline_data,
            'zigzag_data': zigzag_data,
            'peak_points': peak_points,
            'valley_points': valley_points,
            'zigzag_raw': zigzag_raw_serializable
        }
    
    def create_html_chart(self, chart_data: Dict[str, Any], symbol: str, output_file: str = "zigzag_chart.html"):
        """创建HTML图表文件"""
        html_content = self._generate_html_template(chart_data, symbol)

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"图表已生成: {output_file}")
        return output_file

    def _generate_html_template(self, chart_data: Dict[str, Any], symbol: str) -> str:
        """生成HTML模板"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ZigZag算法可视化 - {symbol}</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }}
        .chart-container {{
            width: 100%;
            height: 800px;
            padding: 20px;
            box-sizing: border-box;
        }}
        .info {{
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }}
        .info h3 {{
            margin-top: 0;
            color: #495057;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}
        .stat-item {{
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }}
        .stat-label {{
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }}
        .stat-value {{
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ZigZag算法可视化分析</h1>
            <h2>{symbol}</h2>
            <p>包含动态阈值的ZigZag算法，支持峰谷点标注和趋势线分析</p>
        </div>

        <div class="chart-container">
            <div id="chart" style="width: 100%; height: 100%;"></div>
        </div>

        <div class="info">
            <h3>📊 统计信息</h3>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-label">数据点数量</div>
                    <div class="stat-value">{len(chart_data['kline_data'])}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">ZigZag转折点</div>
                    <div class="stat-value">{len(chart_data['zigzag_data'])}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">峰点数量</div>
                    <div class="stat-value">{len(chart_data['peak_points'])}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">谷点数量</div>
                    <div class="stat-value">{len(chart_data['valley_points'])}</div>
                </div>
            </div>

            <h3>🎯 使用说明</h3>
            <ul>
                <li><strong>峰谷点标注</strong>: 红色三角形表示峰点，绿色倒三角形表示谷点</li>
                <li><strong>鼠标悬停</strong>: 将鼠标移到峰谷点上可查看详细信息</li>
                <li><strong>趋势线分析</strong>: 点击峰谷点可显示相关的峰峰连线和谷谷连线</li>
                <li><strong>交叉点识别</strong>: 系统会自动识别并标注重要的趋势线交叉点</li>
                <li><strong>缩放平移</strong>: 支持鼠标滚轮缩放和拖拽平移</li>
            </ul>
        </div>
    </div>

    <script>
        // 图表数据
        const klineData = {json.dumps(chart_data['kline_data'])};
        const zigzagData = {json.dumps(chart_data['zigzag_data'])};
        const peakPoints = {json.dumps(chart_data['peak_points'])};
        const valleyPoints = {json.dumps(chart_data['valley_points'])};
        const zigzagRaw = {json.dumps(chart_data['zigzag_raw'])};

        // 初始化图表
        const chart = echarts.init(document.getElementById('chart'));

        // 趋势线分析器
        class TrendLineAnalyzer {{
            static findPeakToPeakLines(zigzagRaw, currentIndex, lookback = 5) {{
                const peaks = zigzagRaw.filter(point => point[2] === 'HIGH' && point[0] <= currentIndex);
                if (peaks.length < 2) return [];

                const recentPeaks = peaks.slice(-Math.min(lookback, peaks.length));
                const lines = [];

                for (let i = 0; i < recentPeaks.length - 1; i++) {{
                    for (let j = i + 1; j < recentPeaks.length; j++) {{
                        const peak1 = recentPeaks[i];
                        const peak2 = recentPeaks[j];

                        lines.push({{
                            type: 'peak_to_peak',
                            start: {{ index: peak1[0], price: peak1[1], date: peak1[3] }},
                            end: {{ index: peak2[0], price: peak2[1], date: peak2[3] }}
                        }});
                    }}
                }}

                return lines;
            }}

            static findValleyToValleyLines(zigzagRaw, currentIndex, lookback = 5) {{
                const valleys = zigzagRaw.filter(point => point[2] === 'LOW' && point[0] <= currentIndex);
                if (valleys.length < 2) return [];

                const recentValleys = valleys.slice(-Math.min(lookback, valleys.length));
                const lines = [];

                for (let i = 0; i < recentValleys.length - 1; i++) {{
                    for (let j = i + 1; j < recentValleys.length; j++) {{
                        const valley1 = recentValleys[i];
                        const valley2 = recentValleys[j];

                        lines.push({{
                            type: 'valley_to_valley',
                            start: {{ index: valley1[0], price: valley1[1], date: valley1[3] }},
                            end: {{ index: valley2[0], price: valley2[1], date: valley2[3] }}
                        }});
                    }}
                }}

                return lines;
            }}
        }}

        // 图表配置
        const option = {{
            title: {{
                text: 'ZigZag算法可视化 - {symbol}',
                left: 'center',
                textStyle: {{
                    color: '#333',
                    fontSize: 18
                }}
            }},
            tooltip: {{
                trigger: 'axis',
                axisPointer: {{
                    type: 'cross'
                }},
                formatter: function(params) {{
                    if (params && params.length > 0) {{
                        const data = params[0];
                        if (data.seriesName === 'K线') {{
                            const values = data.data;
                            return `
                                <div style="padding: 10px;">
                                    <div style="font-weight: bold; margin-bottom: 8px;">${{values[0]}}</div>
                                    <div>开盘: <span style="color: #666;">${{values[1].toFixed(2)}}</span></div>
                                    <div>收盘: <span style="color: #666;">${{values[2].toFixed(2)}}</span></div>
                                    <div>最低: <span style="color: #666;">${{values[3].toFixed(2)}}</span></div>
                                    <div>最高: <span style="color: #666;">${{values[4].toFixed(2)}}</span></div>
                                    <div>成交量: <span style="color: #666;">${{values[5].toLocaleString()}}</span></div>
                                </div>
                            `;
                        }}
                    }}
                    return '';
                }}
            }},
            legend: {{
                data: ['K线', '峰点', '谷点'],
                top: 30
            }},
            grid: {{
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            }},
            xAxis: {{
                type: 'category',
                data: klineData.map(item => item[0]),
                scale: true,
                boundaryGap: false,
                axisLine: {{ onZero: false }},
                splitLine: {{ show: false }},
                min: 'dataMin',
                max: 'dataMax'
            }},
            yAxis: {{
                scale: true,
                splitArea: {{
                    show: true
                }}
            }},
            dataZoom: [
                {{
                    type: 'inside',
                    start: 0,
                    end: 100
                }},
                {{
                    show: true,
                    type: 'slider',
                    top: '90%',
                    start: 0,
                    end: 100
                }}
            ],
            series: [
                {{
                    name: 'K线',
                    type: 'candlestick',
                    data: klineData.map(item => [item[1], item[2], item[3], item[4]]),
                    itemStyle: {{
                        color: '#ef232a',
                        color0: '#14b143',
                        borderColor: '#ef232a',
                        borderColor0: '#14b143'
                    }}
                }},
                {{
                    name: '峰点',
                    type: 'scatter',
                    coordinateSystem: 'cartesian2d',
                    data: peakPoints.map(point => [point.coord[0], point.coord[1]]),
                    symbol: 'triangle',
                    symbolSize: 12,
                    itemStyle: {{
                        color: '#ff4757'
                    }},
                    tooltip: {{
                        formatter: function(params) {{
                            const point = peakPoints[params.dataIndex];
                            return `
                                <div style="padding: 10px;">
                                    <div style="font-weight: bold; color: #ff4757; margin-bottom: 8px;">📈 峰点</div>
                                    <div>日期: <span style="color: #666;">${{point.coord[0]}}</span></div>
                                    <div>价格: <span style="color: #666;">${{point.value.toFixed(2)}}</span></div>
                                    <div>索引: <span style="color: #666;">${{point.index}}</span></div>
                                </div>
                            `;
                        }}
                    }}
                }},
                {{
                    name: '谷点',
                    type: 'scatter',
                    coordinateSystem: 'cartesian2d',
                    data: valleyPoints.map(point => [point.coord[0], point.coord[1]]),
                    symbol: 'triangle',
                    symbolRotate: 180,
                    symbolSize: 12,
                    itemStyle: {{
                        color: '#2ed573'
                    }},
                    tooltip: {{
                        formatter: function(params) {{
                            const point = valleyPoints[params.dataIndex];
                            return `
                                <div style="padding: 10px;">
                                    <div style="font-weight: bold; color: #2ed573; margin-bottom: 8px;">📉 谷点</div>
                                    <div>日期: <span style="color: #666;">${{point.coord[0]}}</span></div>
                                    <div>价格: <span style="color: #666;">${{point.value.toFixed(2)}}</span></div>
                                    <div>索引: <span style="color: #666;">${{point.index}}</span></div>
                                </div>
                            `;
                        }}
                    }}
                }}
            ]
        }};

        // 设置图表选项
        chart.setOption(option);

        // 响应式调整
        window.addEventListener('resize', function() {{
            chart.resize();
        }});

        // 点击事件处理 - 显示趋势线
        chart.on('click', function(params) {{
            if (params.seriesName === '峰点' || params.seriesName === '谷点') {{
                const currentIndex = params.seriesName === '峰点' ?
                    peakPoints[params.dataIndex].index :
                    valleyPoints[params.dataIndex].index;

                // 计算趋势线
                const peakLines = TrendLineAnalyzer.findPeakToPeakLines(zigzagRaw, currentIndex, 3);
                const valleyLines = TrendLineAnalyzer.findValleyToValleyLines(zigzagRaw, currentIndex, 3);

                // 添加趋势线到图表
                const newSeries = [...option.series];

                // 添加峰峰连线
                peakLines.forEach((line, index) => {{
                    newSeries.push({{
                        name: `峰峰连线${{index + 1}}`,
                        type: 'line',
                        data: [
                            [line.start.date, line.start.price],
                            [line.end.date, line.end.price]
                        ],
                        lineStyle: {{
                            color: '#ff6b6b',
                            width: 2,
                            type: 'dashed'
                        }},
                        symbol: 'none',
                        animation: false
                    }});
                }});

                // 添加谷谷连线
                valleyLines.forEach((line, index) => {{
                    newSeries.push({{
                        name: `谷谷连线${{index + 1}}`,
                        type: 'line',
                        data: [
                            [line.start.date, line.start.price],
                            [line.end.date, line.end.price]
                        ],
                        lineStyle: {{
                            color: '#4ecdc4',
                            width: 2,
                            type: 'dashed'
                        }},
                        symbol: 'none',
                        animation: false
                    }});
                }});

                // 更新图表
                chart.setOption({{
                    series: newSeries
                }});

                // 3秒后清除趋势线
                setTimeout(() => {{
                    chart.setOption({{
                        series: option.series
                    }});
                }}, 3000);
            }}
        }});

        console.log('ZigZag可视化图表已加载完成');
        console.log('数据统计:', {{
            'K线数据点': klineData.length,
            'ZigZag转折点': zigzagData.length,
            '峰点': peakPoints.length,
            '谷点': valleyPoints.length
        }});
    </script>
</body>
</html>
        """


def create_sample_stock_data() -> List[StockData]:
    """创建示例股票数据用于演示"""
    from datetime import datetime, timedelta
    import random
    import math

    base_date = datetime(2024, 1, 1)
    base_price = 100.0
    stock_data = []

    # 生成更真实的股票数据，包含趋势和波动
    for i in range(200):  # 200个交易日
        date = base_date + timedelta(days=i)

        # 添加长期趋势
        trend = math.sin(i * 0.02) * 20

        # 添加随机波动
        noise = random.uniform(-3, 3)

        # 添加一些大的波动（模拟重要事件）
        if i % 50 == 0:
            noise += random.uniform(-10, 10)

        close_price = base_price + trend + noise + (i * 0.1)  # 轻微上涨趋势

        # 生成OHLC数据
        open_price = close_price + random.uniform(-1, 1)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)
        volume = random.randint(800000, 1500000)

        stock_data.append(StockData(
            symbol="DEMO",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(round(close_price, 2))),
            volume=volume
        ))

    return stock_data


def main():
    """主函数"""
    print("=== ZigZag算法验证和ECharts可视化演示 ===\n")

    # 1. 获取股票数据
    print("1. 获取股票数据...")
    try:
        stock_code = "003021"  # 兆威机电
        start_date = "20230101"
        end_date = "20241201"

        print(f"   股票代码: {stock_code}")
        print(f"   时间范围: {start_date} - {end_date}")

        df = get_stock_data(stock_code, start_date, end_date)
        stock_data = dataframe_to_stock_data(df, stock_code)

        print(f"   获取数据点: {len(stock_data)}个")
        if stock_data:
            print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")

    except Exception as e:
        print(f"   获取股票数据失败: {e}")
        print("   使用示例数据代替...")
        stock_data = create_sample_stock_data()
        stock_code = "DEMO"

    if not stock_data:
        print("   没有可用的股票数据")
        return

    # 2. 初始化ZigZag分析器
    print("\n2. 初始化ZigZag分析器...")

    # 测试不同的参数配置
    configs = [
        {"threshold": 0.03, "dynamic": True, "name": "动态阈值(3%基准)"},
        {"threshold": 0.05, "dynamic": True, "name": "动态阈值(5%基准)"},
        {"threshold": 0.05, "dynamic": False, "name": "固定阈值(5%)"},
    ]

    for config in configs:
        print(f"\n   测试配置: {config['name']}")

        analyzer = ZigZagAnalyzer(
            base_threshold=config['threshold'],
            dynamic_threshold=config['dynamic'],
            atr_period=14
        )

        # 3. 计算ZigZag转折点
        print("   计算ZigZag转折点...")
        zigzag_points = analyzer.calculate_zigzag_points(stock_data)

        print(f"   发现转折点: {len(zigzag_points)}个")
        if zigzag_points:
            peaks = [p for p in zigzag_points if p[2] == 'HIGH']
            valleys = [p for p in zigzag_points if p[2] == 'LOW']
            print(f"   峰点: {len(peaks)}个, 谷点: {len(valleys)}个")

            # 显示前几个转折点
            print("   前5个转折点:")
            for i, (idx, price, point_type, date) in enumerate(zigzag_points[:5]):
                print(f"     {i+1}. {date} - {point_type}: ¥{float(price):.2f} (索引:{idx})")

        # 4. 生成ECharts可视化
        print("   生成ECharts可视化...")
        visualizer = EChartsVisualizer()
        chart_data = visualizer.generate_chart_data(stock_data, zigzag_points)

        # 生成HTML文件
        config_name = config['name'].replace('(', '_').replace(')', '').replace('%', 'pct')
        output_file = f"zigzag_chart_{stock_code}_{config_name}.html"
        visualizer.create_html_chart(chart_data, f"{stock_code} - {config['name']}", output_file)

        print(f"   ✅ 可视化文件已生成: {output_file}")

    print(f"\n🎉 ZigZag算法验证完成!")
    print("📊 可以打开生成的HTML文件查看可视化结果")
    print("🎯 支持的交互功能:")
    print("   - 鼠标悬停查看K线和峰谷点详情")
    print("   - 点击峰谷点显示趋势线分析")
    print("   - 滚轮缩放和拖拽平移")
    print("   - 数据区间选择")


if __name__ == "__main__":
    main()
